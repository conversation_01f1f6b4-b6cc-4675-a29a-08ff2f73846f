APP_NAME=Laravel
APP_ENV=testing
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# CRITICAL: Use separate test database to prevent production data loss
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Alternative: Use separate MySQL test database
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=create-my-run-test
# DB_USERNAME=root
# DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

MAIL_MAILER=array

# Disable external services during testing
MAPBOX_ACCESS_TOKEN=test-token
MIX_MAPBOX_ACCESS_TOKEN=test-token
