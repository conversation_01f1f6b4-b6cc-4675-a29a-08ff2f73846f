<template>
    <div class="max-w-4xl mx-auto">
        <div class="bg-white p-8 rounded-lg shadow-md">
            <h1 class="text-3xl font-bold text-strava-gray mb-6">Profile</h1>
            
            <div v-if="user" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h2 class="text-xl font-semibold text-strava-grayMedium mb-4">Personal Information</h2>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-strava-grayLight mb-1">Name</label>
                                <p class="text-strava-gray font-medium">{{ user.name }}</p>
                            </div>
                            <div>
                                <label class="block text-strava-grayLight mb-1">Email</label>
                                <p class="text-strava-gray font-medium">{{ user.email }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h2 class="text-xl font-semibold text-strava-grayMedium mb-4">Account Statistics</h2>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-strava-grayLight mb-1">Member Since</label>
                                <p class="text-strava-gray font-medium">{{ memberSince }}</p>
                            </div>
                            <div>
                                <label class="block text-strava-grayLight mb-1">Activities Created</label>
                                <p class="text-strava-gray font-medium">{{ activitiesCount }}</p>
                            </div>
                            <div>
                                <label class="block text-strava-grayLight mb-1">Available Tokens</label>
                                <p class="text-strava-gray font-medium">{{ user.tokens || 0 }}</p>
                                <p class="text-sm text-strava-grayLight mt-1">Tokens are used to create and export activities</p>
                                <router-link :to="{ name: 'purchase-tokens' }" class="inline-block mt-2 text-sm bg-strava-orange text-white py-1 px-3 rounded hover:bg-strava-orangeLight">
                                    Purchase More Tokens
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div v-else class="text-center py-8">
                <p class="text-strava-grayLight">Please log in to view your profile.</p>
                <router-link :to="{ name: 'login' }" class="inline-block mt-4 bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">
                    Login
                </router-link>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { getUser } from '../utils/auth';
import axios from 'axios';

export default {
    name: 'Profile',
    setup() {
        const user = ref(null);
        const memberSince = ref('');
        const activitiesCount = ref(0);

        const fetchUserData = async () => {
            try {
                // Get the most up-to-date user data from the server
                const response = await axios.get('/api/user');
                user.value = response.data;
                
                // Format the created_at timestamp as member since date
                if (user.value && user.value.created_at) {
                    const createdDate = new Date(user.value.created_at);
                    memberSince.value = createdDate.toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                    });
                }
                // Set a demo value for activities count
                activitiesCount.value = 0;
            } catch (error) {
                console.error('Error fetching user data:', error);
                // Fall back to localStorage if API call fails
                user.value = getUser();
                
                if (user.value && user.value.created_at) {
                    const createdDate = new Date(user.value.created_at);
                    memberSince.value = createdDate.toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                    });
                } else if (user.value) {
                    // Fallback if created_at is not available
                    memberSince.value = 'Unknown';
                }
                activitiesCount.value = 0;
            }
        };

        onMounted(() => {
            fetchUserData();
        });

        return {
            user,
            memberSince,
            activitiesCount
        };
    }
}
</script>
