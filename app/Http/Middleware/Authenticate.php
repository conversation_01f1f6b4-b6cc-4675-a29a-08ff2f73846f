<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        // For API routes, return null to get 401 JSON response
        if ($request->is('api/*')) {
            return null;
        }

        // For web routes that don't expect JSON, redirect to login
        if (! $request->expectsJson()) {
            return route('login');
        }

        // For other JSON requests, return null to get 401 JSON response
        return null;
    }
}
