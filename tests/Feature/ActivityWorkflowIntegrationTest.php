<?php

use App\Models\Activity;
use App\Models\ExportedActivity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->withManyTokens()->create();

    $this->completeActivityData = [
        'name' => 'Integration Test Run',
        'route_points' => [
            [37.7749, -122.4194],
            [37.7750, -122.4195],
            [37.7751, -122.4196],
            [37.7752, -122.4197],
            [37.7753, -122.4198]
        ],
        'activity_date' => '2024-01-15',
        'activity_time' => '08:30:00',
        'duration' => 2400, // 40 minutes
        'pace' => 6.0, // 6:00 min/km
        'activity_type' => 'running'
    ];

    $this->gpxExportData = [
        'name' => 'Integration Test Run',
        'routePoints' => [
            [37.7749, -122.4194],
            [37.7750, -122.4195],
            [37.7751, -122.4196],
            [37.7752, -122.4197],
            [37.7753, -122.4198]
        ],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 2400,
        'pace' => 6.0,
        'distance' => 8000, // 8km
        'includeHeartRate' => true,
        'heartRate' => 155,
        'includeElevation' => true,
        'includeCadence' => true,
        'cadence' => 175
    ];
});

// Complete Activity Lifecycle Tests

it('creates, exports, and manages complete activity workflow', function () {
    Sanctum::actingAs($this->user);

    // Capture initial token count
    $initialTokens = $this->user->tokens;

    // Step 1: Create an activity
    $createResponse = $this->postJson('/api/activities', $this->completeActivityData);
    $createResponse->assertStatus(201);

    $activityId = $createResponse->json('activity.id');
    expect($activityId)->toBeInt();

    // Step 2: Verify activity was created correctly
    $showResponse = $this->getJson("/api/activities/{$activityId}");
    $showResponse->assertStatus(200)
        ->assertJsonFragment([
            'name' => 'Integration Test Run',
            'activity_type' => 'running',
            'pace' => 6.0,
            'formatted_pace' => '6:00 min/km'
        ]);

    // Step 3: Export activity to GPX
    $exportData = array_merge($this->gpxExportData, ['activity_id' => $activityId]);
    $exportResponse = $this->postJson('/api/export/gpx', $exportData);
    $exportResponse->assertStatus(200);

    $gpxContent = $exportResponse->json('content');
    expect($gpxContent)->toContain('Integration Test Run');
    expect($gpxContent)->toContain('<gpx');

    // Step 4: Verify exported activity was logged
    $exportedActivities = $this->getJson('/api/exported-activities');
    $exportedActivities->assertStatus(200)
        ->assertJsonCount(1);

    $exportId = $exportedActivities->json('0.id');

    // Step 5: Download the exported file
    $downloadResponse = $this->get("/api/exported-activities/{$exportId}/download");
    $downloadResponse->assertStatus(200)
        ->assertHeader('Content-Type', 'application/gpx+xml');

    // Step 6: Update the activity
    $updateData = ['name' => 'Updated Integration Test Run', 'pace' => 5.5];
    $updateResponse = $this->putJson("/api/activities/{$activityId}", $updateData);
    $updateResponse->assertStatus(200);

    // Step 7: Verify update
    $updatedActivity = $this->getJson("/api/activities/{$activityId}");
    $updatedActivity->assertStatus(200)
        ->assertJsonFragment([
            'name' => 'Updated Integration Test Run',
            'pace' => 5.5,
            'formatted_pace' => '5:30 min/km'
        ]);

    // Step 8: Verify token was used
    $tokenResponse = $this->getJson('/api/tokens');
    expect($tokenResponse->json('tokens'))->toBe($initialTokens - 1);
});

it('handles multiple exports of same activity', function () {
    Sanctum::actingAs($this->user);

    // Create activity
    $createResponse = $this->postJson('/api/activities', $this->completeActivityData);
    $activityId = $createResponse->json('activity.id');

    // Export same activity multiple times
    $exportData = array_merge($this->gpxExportData, ['activity_id' => $activityId]);

    $this->postJson('/api/export/gpx', $exportData)->assertStatus(200);
    $this->postJson('/api/export/gpx', $exportData)->assertStatus(200);
    $this->postJson('/api/export/gpx', $exportData)->assertStatus(200);

    // Verify multiple export records
    $exportedActivities = $this->getJson('/api/exported-activities');
    $exportedActivities->assertStatus(200)
        ->assertJsonCount(3);

    // Verify all exports reference same activity
    $exports = $exportedActivities->json();
    foreach ($exports as $export) {
        expect($export['activity_id'])->toBe($activityId);
    }
});

it('handles activity deletion with cascade', function () {
    Sanctum::actingAs($this->user);

    // Create and export activity
    $createResponse = $this->postJson('/api/activities', $this->completeActivityData);
    $activityId = $createResponse->json('activity.id');

    $exportData = array_merge($this->gpxExportData, ['activity_id' => $activityId]);
    $this->postJson('/api/export/gpx', $exportData)->assertStatus(200);

    // Verify export exists
    $this->getJson('/api/exported-activities')->assertJsonCount(1);

    // Delete activity
    $deleteResponse = $this->deleteJson("/api/activities/{$activityId}");
    $deleteResponse->assertStatus(200);

    // Verify activity is deleted
    $this->getJson("/api/activities/{$activityId}")->assertStatus(404);

    // Verify exported activities are also deleted (cascade)
    $this->getJson('/api/exported-activities')->assertJsonCount(0);
});

// Different Activity Types Workflow Tests

it('handles running activity workflow', function () {
    Sanctum::actingAs($this->user);

    $runningData = array_merge($this->completeActivityData, [
        'activity_type' => 'running',
        'pace' => 5.0
    ]);

    $createResponse = $this->postJson('/api/activities', $runningData);
    $activityId = $createResponse->json('activity.id');

    $exportData = array_merge($this->gpxExportData, [
        'activity_id' => $activityId,
        'pace' => 5.0
    ]);

    $exportResponse = $this->postJson('/api/export/gpx', $exportData);
    $exportResponse->assertStatus(200);

    $content = $exportResponse->json('content');
    expect($content)->toContain('<type>running</type>');
    expect($exportResponse->json('pace_display'))->toBe('5:00 min/km');
});

it('handles cycling activity workflow', function () {
    Sanctum::actingAs($this->user);

    $cyclingData = array_merge($this->completeActivityData, [
        'activity_type' => 'cycling',
        'pace' => 3.0 // Faster pace for cycling
    ]);

    $createResponse = $this->postJson('/api/activities', $cyclingData);
    $activityId = $createResponse->json('activity.id');

    $exportData = array_merge($this->gpxExportData, [
        'activity_id' => $activityId,
        'pace' => 3.0,
        'cadence' => 90 // Cycling cadence
    ]);

    $exportResponse = $this->postJson('/api/export/gpx', $exportData);
    $exportResponse->assertStatus(200);

    $content = $exportResponse->json('content');
    expect($content)->toContain('<gpxtpx:cad>90</gpxtpx:cad>');
    expect($exportResponse->json('pace_display'))->toBe('3:00 min/km');
});

it('handles hiking activity workflow', function () {
    Sanctum::actingAs($this->user);

    $hikingData = array_merge($this->completeActivityData, [
        'activity_type' => 'hiking',
        'pace' => 10.0 // Slower pace for hiking
    ]);

    $createResponse = $this->postJson('/api/activities', $hikingData);
    $activityId = $createResponse->json('activity.id');

    $exportData = array_merge($this->gpxExportData, [
        'activity_id' => $activityId,
        'pace' => 10.0,
        'heartRate' => 130 // Lower heart rate for hiking
    ]);

    $exportResponse = $this->postJson('/api/export/gpx', $exportData);
    $exportResponse->assertStatus(200);

    $content = $exportResponse->json('content');
    expect($content)->toContain('<gpxtpx:hr>130</gpxtpx:hr>');
    expect($exportResponse->json('pace_display'))->toBe('10:00 min/km');
});

// Token Management Integration Tests

it('tracks token usage across multiple operations', function () {
    Sanctum::actingAs($this->user);
    $initialTokens = $this->user->tokens;

    // Create activity (no token used)
    $this->postJson('/api/activities', $this->completeActivityData);
    $this->getJson('/api/tokens')->assertJson(['tokens' => $initialTokens]);

    // Export activity (token used)
    $this->postJson('/api/export/gpx', $this->gpxExportData);
    $this->getJson('/api/tokens')->assertJson(['tokens' => $initialTokens - 1]);

    // Export again (another token used)
    $this->postJson('/api/export/gpx', $this->gpxExportData);
    $this->getJson('/api/tokens')->assertJson(['tokens' => $initialTokens - 2]);

    // Add tokens
    $this->postJson('/api/tokens', ['tokens' => 5]);
    $this->getJson('/api/tokens')->assertJson(['tokens' => $initialTokens - 2 + 5]);
});

it('prevents operations when tokens are exhausted', function () {
    $userWithOneToken = User::factory()->create(['tokens' => 1]);
    Sanctum::actingAs($userWithOneToken);

    // First export should succeed
    $this->postJson('/api/export/gpx', $this->gpxExportData)->assertStatus(200);

    // Second export should fail
    $this->postJson('/api/export/gpx', $this->gpxExportData)->assertStatus(403);

    // Verify no export was created
    $this->getJson('/api/exported-activities')->assertJsonCount(1);
});

// Error Handling Integration Tests

it('handles validation errors gracefully throughout workflow', function () {
    Sanctum::actingAs($this->user);

    // Try to create activity with invalid data
    $invalidData = array_merge($this->completeActivityData, ['pace' => 'invalid']);
    $this->postJson('/api/activities', $invalidData)->assertStatus(422);

    // Try to export with invalid data
    $invalidExportData = array_merge($this->gpxExportData, ['duration' => 'invalid']);
    $this->postJson('/api/export/gpx', $invalidExportData)->assertStatus(422);

    // Verify no records were created
    $this->getJson('/api/activities')->assertJsonCount(0);
    $this->getJson('/api/exported-activities')->assertJsonCount(0);
});

it('maintains data consistency on partial failures', function () {
    Sanctum::actingAs($this->user);

    // Create valid activity
    $createResponse = $this->postJson('/api/activities', $this->completeActivityData);
    $activityId = $createResponse->json('activity.id');

    // Try invalid update
    $this->putJson("/api/activities/{$activityId}", ['pace' => 'invalid'])
        ->assertStatus(422);

    // Verify original data is unchanged
    $this->getJson("/api/activities/{$activityId}")
        ->assertJsonFragment(['pace' => 6.0]);
});
