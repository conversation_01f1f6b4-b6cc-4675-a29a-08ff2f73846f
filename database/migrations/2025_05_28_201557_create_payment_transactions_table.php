<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('payos_order_code')->unique();
            $table->string('payos_payment_link_id')->nullable();
            $table->string('package_type'); // basic, standard, premium
            $table->integer('amount'); // Amount in cents
            $table->integer('tokens'); // Number of tokens purchased
            $table->string('status')->default('PENDING'); // PENDING, PAID, CANCELLED, EXPIRED
            $table->text('description');
            $table->json('payos_response')->nullable(); // Store full PayOS response
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('payos_order_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_transactions');
    }
}
