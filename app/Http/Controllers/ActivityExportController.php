<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\ExportedActivity;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use phpGPX\Models\Extensions;
use phpGPX\Models\Extensions\TrackPointExtension;
use phpGPX\Models\GpxFile;
use phpGPX\Models\Link;
use phpGPX\Models\Metadata;
use phpGPX\Models\Point;
use phpGPX\Models\Segment;
use phpGPX\Models\Track;
use phpGPX\phpGPX;

class ActivityExportController extends Controller
{
    /**
     * ISO 8601 datetime format constant for GPX files
     */
    private const GPX_DATETIME_FORMAT = 'Y-m-d\TH:i:s\Z';

    /**
     * Generate GPX file from activity data
     *
     * @param Request $request
     * @return Response
     */
    public function generateGpx(Request $request)
    {
        // Check if user is authenticated
        $user = Auth::user();

        // Check if user has available tokens (only for authenticated users)
        if ($user && !$user->hasAvailableTokens()) {
            return response()->json([
                'message' => 'You do not have enough tokens to export this activity.',
                'tokens_available' => 0
            ], 403);
        }

        // Validate incoming request
        $validated = $request->validate([
            'activity_id' => 'nullable|exists:activities,id',
            'name' => 'required|string',
            'routePoints' => 'present|array',
            'activityDate' => 'required|string',
            'activityTime' => 'required|string',
            'duration' => 'required|numeric',
            'pace' => 'required|numeric',
            'paceVariability' => 'nullable|numeric|min:0|max:100', // Pace variability percentage (0-100%)
            'includeHeartRate' => 'boolean',
            'includeCadence' => 'boolean',
            'includeElevation' => 'boolean',
            'heartRate' => 'nullable|numeric',
            'cadence' => 'nullable|numeric',
            'distance' => 'nullable|numeric', // Add distance validation
            'timezone' => 'nullable|string', // Optional timezone parameter
        ]);

        try {

            // Format the route points using phpGPX library with realistic pace variations
            $formattedPoints = $this->formatRoutePoints(
                $validated['routePoints'],
                $validated['activityDate'],
                $validated['activityTime'],
                $validated['duration'],
                $validated['includeHeartRate'] ?? false,
                $validated['includeElevation'] ?? false,
                $validated['heartRate'] ?? null,
                $validated['includeCadence'] ?? false,
                $validated['cadence'] ?? null,
                $validated['timezone'] ?? null,
                $validated['pace'],
                $validated['paceVariability'] ?? 5.0,
                $validated['distance'] ?? null
            );
            $formattedPace = $this->formatPaceForGpx($validated['pace']);
            $displayPace = Activity::formatPaceDisplay($validated['pace']);

            // Generate filename
            $filename = $this->sanitizeFileName($validated['name']) . '.gpx';

            // Generate GPX content using phpGPX library
            $gpxContent = $this->createGpxContent(
                $validated['name'],
                $formattedPoints,
                $formattedPace,
                $validated['distance'] ?? null,
                $validated['paceVariability'] ?? 5.0 // Default to 5% if not provided
            );

            // For authenticated users, log the export and use a token
            if ($user) {
                // Use a token
                $user->useToken();

                // Get activity ID from request or find/create the activity
                $activityId = $validated['activity_id'] ?? null;

                if (!$activityId) {

                    $activity = Activity::create([
                        'user_id' => $user->id,
                        'name' => $validated['name'],
                        'route_points' => $validated['routePoints'],
                        'activity_date' => $validated['activityDate'],
                        'activity_time' => $validated['activityTime'],
                        'duration' => $validated['duration'],
                        'pace' => $formattedPace,
                        'pace_variability' => $validated['paceVariability'] ?? 5.0,
                        'distance' => $validated['distance'] ?? null,
                        'activity_type' => 'run'
                    ]);
                    $activityId = $activity->id;
                }

                // Save exported activity data
                $exportedActivity = ExportedActivity::create([
                    'user_id' => $user->id,
                    'activity_id' => $activityId,
                    'format' => 'gpx',
                    'file_name' => $filename,
                    'export_data' => $gpxContent
                ]);

                // Return the GPX content with token information
                return response()->json([
                    'success' => true,
                    'filename' => $filename,
                    'content' => $gpxContent,
                    'mimeType' => 'application/gpx+xml',
                    'tokens_remaining' => $user->tokens,
                    'pace_display' => $displayPace
                ]);
            }

            // For non-authenticated users, just return the GPX content
            return response()->json([
                'success' => true,
                'filename' => $filename,
                'content' => $gpxContent,
                'mimeType' => 'application/gpx+xml',
                'pace_display' => $displayPace
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Create GPX content using phpGPX library
     *
     * @param string $name Activity name
     * @param array $points Formatted route points
     * @param float $pace Pace in minutes per kilometer
     * @param float|null $distance Distance in meters (optional)
     * @param float $paceVariability Pace variability percentage (0-100)
     * @return string GPX file content
     */
    private function createGpxContent(string $name, array $points, float $pace, ?float $distance = null, float $paceVariability = 5.0): string
    {
        // Configure phpGPX
        phpGPX::$PRETTY_PRINT = true;
        phpGPX::$DATETIME_FORMAT = 'Y-m-d\TH:i:s\Z'; // Ensure consistent datetime format
        phpGPX::$DATETIME_TIMEZONE_OUTPUT = 'UTC';
        phpGPX::$CALCULATE_STATS = true; // Enable stats calculation
        phpGPX::$APPLY_ELEVATION_SMOOTHING = true;
        phpGPX::$ELEVATION_SMOOTHING_THRESHOLD = 2;
        phpGPX::$ELEVATION_SMOOTHING_SPIKES_THRESHOLD = null;

        // Create new GPX file
        $gpxFile = new GpxFile();

        // Set up metadata
        $gpxFile->metadata = new Metadata();
        $gpxFile->metadata->time = new \DateTime();
        $gpxFile->metadata->description = "Activity exported from Garmin Connect using Create My Run";

        // Create a link to the app
        $link = new Link();
        $link->href = "https://create-my.run/";
        $link->text = "Create My Run";
        $gpxFile->metadata->links[] = $link;

        // Create a new track
        $track = new Track();
        $track->name = $name;
        $track->type = 'running';
        $track->source = 'Garmin';
        $track->stats = new \phpGPX\Models\Stats();
        $track->stats->averagePace = $pace;

        // Create a track segment
        $segment = new Segment();

        // Add points to segment
        foreach ($points as $index => $pointData) {
            $point = new Point(Point::TRACKPOINT);
            $point->latitude = $pointData['lat'];
            $point->longitude = $pointData['lng'];

            // Set elevation if available
            if (isset($pointData['elevation'])) {
                $point->elevation = $pointData['elevation'];
            }

            // Convert string time to DateTime object
            if (isset($pointData['time'])) {
                $point->time = new \DateTime($pointData['time']);
            }

            // Add heart rate extension if available
            if (isset($pointData['heartRate'])) {
                $extensions = new \phpGPX\Models\Extensions();
                $trackPointExt = new \phpGPX\Models\Extensions\TrackPointExtension();
                $trackPointExt->hr = $pointData['heartRate'];
                $extensions->trackPointExtension = $trackPointExt;
                $point->extensions = $extensions;
            }

            // Add cadence extension if available
            if (isset($pointData['cadence'])) {
                if (!isset($point->extensions)) {
                    $point->extensions = new \phpGPX\Models\Extensions();
                    $point->extensions->trackPointExtension = new \phpGPX\Models\Extensions\TrackPointExtension();
                }
                $point->extensions->trackPointExtension->cad = $pointData['cadence'];
            }

            // Add speed extension based on realistic pace variations
            if ($pace > 0 && isset($pointData['segmentPace'])) {
                // Use the segment-specific pace calculated in formatRoutePoints
                $segmentPace = $pointData['segmentPace'];

                // Convert pace (min/km) to speed (m/s)
                // Formula: speed = distance / time = 1000m / (pace_in_minutes * 60s)
                $speedMps = 1000.0 / ($segmentPace * 60.0);

                if (!isset($point->extensions)) {
                    $point->extensions = new \phpGPX\Models\Extensions();
                    $point->extensions->trackPointExtension = new \phpGPX\Models\Extensions\TrackPointExtension();
                }

                // Set speed extension (some GPX readers use this for moving time calculation)
                // Ensure speed is reasonable (0.5 m/s to 15 m/s for various activities)
                $speedMps = max(0.5, min(15.0, $speedMps));
                $point->extensions->trackPointExtension->speed = $speedMps;
            } else if ($pace > 0) {
                // Fallback to base pace if no segment pace is available
                $speedMps = 1000.0 / ($pace * 60.0);

                if (!isset($point->extensions)) {
                    $point->extensions = new \phpGPX\Models\Extensions();
                    $point->extensions->trackPointExtension = new \phpGPX\Models\Extensions\TrackPointExtension();
                }

                $speedMps = max(0.5, min(15.0, $speedMps));
                $point->extensions->trackPointExtension->speed = $speedMps;
            }

            $segment->points[] = $point;
        }

        // Add segment to track
        $track->segments[] = $segment;

        // If distance is provided, set it in the track stats
        if ($distance) {
            if (!$track->stats) {
                $track->stats = new \phpGPX\Models\Stats();
            }
            $track->stats->distance = $distance;
        }

        // Calculate track statistics
        $track->recalculateStats();

        // Add track to file
        $gpxFile->tracks[] = $track;

        // Generate XML content
        return $gpxFile->toXML()->saveXML();
    }

    /**
     * Format route points from frontend format to phpGPX format with realistic pace variations
     *
     * @param array $routePoints
     * @param string $date
     * @param string $time
     * @param float $duration
     * @param bool $includeHeartRate
     * @param bool $includeElevation
     * @param int|null $heartRate
     * @param bool $includeCadence
     * @param int|null $cadence
     * @param string|null $timezone
     * @param float $basePace Base pace in minutes per kilometer
     * @param float $paceVariability Pace variability percentage (0-100)
     * @param float|null $totalDistance Total distance in meters
     * @return array
     */
    private function formatRoutePoints(array $routePoints, string $date, string $time, float $duration, bool $includeHeartRate = false, bool $includeElevation = false, ?int $heartRate = null, bool $includeCadence = false, ?int $cadence = null, ?string $timezone = null, float $basePace = 5.0, float $paceVariability = 5.0, ?float $totalDistance = null): array
    {
        $formattedPoints = [];

        // Handle timezone conversion to ensure Strava displays the correct local time
        // The issue: Strava incorrectly ADDS timezone offset when reading GPX files
        // Solution: Subtract the timezone offset so when Strava adds it back, it shows correctly
        if ($timezone) {
            // If timezone is provided, calculate the offset and subtract it
            try {
                $userDateTime = new \DateTime("$date $time", new \DateTimeZone($timezone));
                $timezoneOffset = $userDateTime->getOffset(); // Offset in seconds
                $localTimestamp = strtotime("$date $time");
                // Subtract the offset so when Strava adds it back, we get the original time
                $startTimeUtc = $localTimestamp - $timezoneOffset;
            } catch (\Exception $e) {
                // Fallback: assume UTC+7 (common for Vietnam/SEA region)
                $defaultOffset = 7 * 3600; // 7 hours in seconds
                $localTimestamp = strtotime("$date $time");
                $startTimeUtc = $localTimestamp - $defaultOffset;
            }
        } else {
            // For users without timezone specified, assume UTC+7 (common for Vietnam/SEA region)
            // This is based on the 7-hour difference observed in the user's issue
            $defaultOffset = 7 * 3600; // 7 hours in seconds
            $localTimestamp = strtotime("$date $time");
            $startTimeUtc = $localTimestamp - $defaultOffset;
        }

        $pointCount = count($routePoints);

        if ($pointCount < 1) {
            return $formattedPoints;
        }

        // Calculate total distance and segment distances
        $calculatedDistance = 0;
        $segmentDistances = [];
        $cumulativeDistances = [0]; // First point is at distance 0

        for ($i = 1; $i < $pointCount; $i++) {
            $prevLat = $routePoints[$i-1][0];
            $prevLng = $routePoints[$i-1][1];
            $currentLat = $routePoints[$i][0];
            $currentLng = $routePoints[$i][1];

            // Calculate distance between consecutive points using Haversine formula
            $segmentDistance = $this->calculateDistance($prevLat, $prevLng, $currentLat, $currentLng);
            $segmentDistances[] = $segmentDistance;

            $calculatedDistance += $segmentDistance;
            $cumulativeDistances[] = $calculatedDistance;
        }

        // Use provided total distance parameter if available, otherwise use calculated distance
        $finalDistance = $totalDistance ?? $calculatedDistance; // Use provided or calculated distance
        if ($finalDistance <= 0) {
            $finalDistance = 1000; // Fallback to 1km if no distance calculated
        }

        // Generate realistic pace variations for kilometer segments
        $paceVariations = $this->generateRealisticPaceVariations($finalDistance, $basePace, $paceVariability);

        // Calculate timing for each point based on realistic pace variations
        $allFormattedPoints = [];
        $currentTime = $startTimeUtc;

        foreach ($routePoints as $index => $point) {
            // Calculate which kilometer segment this point belongs to and get appropriate pace
            $currentDistance = $cumulativeDistances[$index];
            $segmentPace = $this->getPaceForDistance($currentDistance, $paceVariations, $basePace, $paceVariability);

            // Calculate time for this point based on realistic pace progression
            if ($index > 0) {
                $segmentDistanceMeters = $cumulativeDistances[$index] - $cumulativeDistances[$index - 1];
                $segmentDistanceKm = $segmentDistanceMeters / 1000.0;
                $segmentTimeSeconds = $segmentPace * 60 * $segmentDistanceKm; // pace is in min/km
                $currentTime += $segmentTimeSeconds;
            }

            $pointTime = $currentTime;

            // For elevation, use realistic values (0-2m) with decimal precision
            $elevation = null;
            if ($includeElevation) {
                $elevation = mt_rand(0, 20) / 10; // 0.0 to 2.0m with one decimal place
            }

            $formattedPoint = [
                'lat' => $point[0],
                'lng' => $point[1],
                'time' => gmdate(self::GPX_DATETIME_FORMAT, $pointTime),
                'segmentPace' => $segmentPace // Add segment-specific pace for speed calculations
            ];

            // Add elevation if requested
            if ($elevation !== null) {
                $formattedPoint['elevation'] = $elevation;
            }

            // Add heart rate if requested
            if ($includeHeartRate && $heartRate) {
                $formattedPoint['heartRate'] = $heartRate;
            }

            // Add cadence if requested
            if ($includeCadence && $cadence) {
                $formattedPoint['cadence'] = $cadence;
            }

            $allFormattedPoints[] = $formattedPoint;

            // Add intermediate points between this point and the next (except for the last point)
            if ($index < count($routePoints) - 1) {
                $nextPoint = $routePoints[$index + 1];
                $nextDistance = $cumulativeDistances[$index + 1];
                $nextSegmentPace = $this->getPaceForDistance($nextDistance, $paceVariations, $basePace, $paceVariability);

                // Calculate next point time based on pace
                $segmentDistanceMeters = $cumulativeDistances[$index + 1] - $cumulativeDistances[$index];
                $segmentDistanceKm = $segmentDistanceMeters / 1000.0;
                $segmentTimeSeconds = $nextSegmentPace * 60 * $segmentDistanceKm;
                $nextPointTime = $pointTime + $segmentTimeSeconds;

                // Calculate time gap between current and next point
                $timeGap = $nextPointTime - $pointTime;

                // If gap is larger than 30 seconds, add intermediate points every 15-30 seconds
                if ($timeGap > 30) {
                    $intermediatePointsCount = min(floor($timeGap / 20), 10); // Max 10 intermediate points

                    for ($i = 1; $i <= $intermediatePointsCount; $i++) {
                        $ratio = $i / ($intermediatePointsCount + 1);

                        // Interpolate position
                        $intermediateLat = $point[0] + ($nextPoint[0] - $point[0]) * $ratio;
                        $intermediateLng = $point[1] + ($nextPoint[1] - $point[1]) * $ratio;

                        // Interpolate time
                        $intermediateTime = $pointTime + ($timeGap * $ratio);

                        // Interpolate elevation if needed
                        $intermediateElevation = null;
                        if ($includeElevation) {
                            $intermediateElevation = mt_rand(0, 20) / 10;
                        }

                        // Calculate intermediate distance and appropriate pace
                        $intermediateDistance = $cumulativeDistances[$index] + ($segmentDistanceMeters * $ratio);
                        $intermediatePace = $this->getPaceForDistance($intermediateDistance, $paceVariations, $basePace, $paceVariability);

                        $intermediatePoint = [
                            'lat' => $intermediateLat,
                            'lng' => $intermediateLng,
                            'time' => gmdate(self::GPX_DATETIME_FORMAT, $intermediateTime),
                            'segmentPace' => $intermediatePace
                        ];

                        // Add elevation if requested
                        if ($intermediateElevation !== null) {
                            $intermediatePoint['elevation'] = $intermediateElevation;
                        }

                        // Add heart rate if requested
                        if ($includeHeartRate && $heartRate) {
                            $intermediatePoint['heartRate'] = $heartRate;
                        }

                        // Add cadence if requested
                        if ($includeCadence && $cadence) {
                            $intermediatePoint['cadence'] = $cadence;
                        }

                        $allFormattedPoints[] = $intermediatePoint;
                    }
                }
            }
        }

        // Process all formatted points and ensure they have required data
        foreach ($allFormattedPoints as $formattedPoint) {
            // Always include elevation as Strava expects it, use existing or generate new
            if (!isset($formattedPoint['elevation'])) {
                $formattedPoint['elevation'] = mt_rand(0, 20) / 10; // 0.0 to 2.0m
            }

            $formattedPoints[] = $formattedPoint;
        }

        return $formattedPoints;
    }

    /**
     * Generate realistic pace variations for kilometer segments
     *
     * @param float $totalDistance Total distance in meters
     * @param float $basePace Base pace in minutes per kilometer
     * @param float $paceVariability Pace variability percentage (0-100)
     * @return array Array of pace values for each kilometer segment
     */
    private function generateRealisticPaceVariations(float $totalDistance, float $basePace, float $paceVariability): array
    {
        $totalKilometers = ceil($totalDistance / 1000.0);
        $paceVariations = [];

        // Convert variability percentage to decimal
        $variabilityDecimal = $paceVariability / 100.0;

        for ($km = 1; $km <= $totalKilometers; $km++) {
            // If variability is 0%, use base pace for all segments
            if ($paceVariability <= 0) {
                $paceVariations[$km] = $basePace;
                continue;
            }

            // Start with base pace
            $kmPace = $basePace;

            // 1. Subtle warm-up effect for first kilometer only (max 5-10% faster)
            if ($km === 1 && $paceVariability >= 5) {
                $warmupBonus = mt_rand(0, 10) / 100.0; // 0-10% faster
                $warmupBonus = min($warmupBonus, $paceVariability / 100.0); // Respect user's variability setting
                $kmPace = $basePace * (1.0 - $warmupBonus); // Faster = lower pace time
            }

            // 2. Apply significant random variation to ALL kilometers for ~1 minute differences
            // Create much larger variations that will show meaningful differences in Strava splits
            $minVariationSeconds = 60; // Minimum 1 minute variation (increased from 30)
            $maxVariationSeconds = 300; // Maximum 5 minutes variation (increased from 180)

            // Scale based on user's pace variability percentage
            $variationRange = ($paceVariability / 100.0) * ($maxVariationSeconds - $minVariationSeconds) + $minVariationSeconds;

            // Generate random variation in seconds, then convert to minutes
            $variationSeconds = (mt_rand(-100, 100) / 100.0) * $variationRange;
            $variationMinutes = $variationSeconds / 60.0;
            $kmPace += $variationMinutes;

            // 3. Very subtle progressive fatigue for longer runs (>10km)
            if ($totalKilometers > 10 && $km > 5) {
                $fatigueProgress = ($km - 5) / ($totalKilometers - 5);
                $fatigueEffect = $fatigueProgress * 0.05 * ($paceVariability / 100.0); // Max 5% slowdown
                $kmPace = $basePace * (1.0 + $fatigueEffect) + $variationMinutes; // Apply fatigue then variation
            }

            // 4. Apply realistic bounds to allow for ~1-2 minute variations
            // Allow for much larger variations while keeping within human performance ranges
            $minReasonablePace = $basePace * 0.60; // No faster than 40% of base pace (more aggressive)
            $maxReasonablePace = $basePace * 1.70; // No slower than 170% of base pace (more aggressive)

            // Allow ±5 minute absolute bound from base pace for much larger variation
            $minAbsolutePace = $basePace - 5.0;
            $maxAbsolutePace = $basePace + 5.0;

            // Use the more restrictive bounds
            $finalMinPace = max($minReasonablePace, $minAbsolutePace);
            $finalMaxPace = min($maxReasonablePace, $maxAbsolutePace);

            $kmPace = max($finalMinPace, min($finalMaxPace, $kmPace));

            $paceVariations[$km] = $kmPace;
        }

        return $paceVariations;
    }

    /**
     * Get the appropriate pace for a given distance based on pace variations
     * Uses interpolation between kilometer segments for smoother transitions
     *
     * @param float $distance Distance in meters
     * @param array $paceVariations Array of pace values for each kilometer
     * @param float $basePace Base pace as fallback
     * @param float $paceVariability Pace variability percentage for sub-km variations
     * @return float Pace in minutes per kilometer
     */
    private function getPaceForDistance(float $distance, array $paceVariations, float $basePace, float $paceVariability = 5.0): float
    {
        if ($distance <= 0) {
            return $basePace;
        }

        $distanceKm = $distance / 1000.0;
        $currentKm = floor($distanceKm);
        $nextKm = $currentKm + 1;

        // For the first kilometer (0-1000m), use KM 1 pace
        if ($currentKm == 0) {
            return $paceVariations[1] ?? $basePace;
        }

        // Get current and next kilometer paces
        $currentPace = $paceVariations[$currentKm] ?? $basePace;
        $nextPace = $paceVariations[$nextKm] ?? $currentPace;

        // Calculate position within the current kilometer (0.0 to 1.0)
        $positionInKm = $distanceKm - $currentKm;

        // Add meaningful randomness within each kilometer to avoid mechanical consistency
        // Create sub-kilometer variations (every 50m gets variation for more granularity)
        $subKmSegment = floor(($distance % 1000) / 50); // 0-19 segments per km (every 50m)
        $randomSeed = $currentKm * 100 + $subKmSegment; // Consistent seed for same segment
        mt_srand($randomSeed);

        // Scale sub-kilometer variation based on pace variability setting
        $maxSubKmVariationSeconds = 40; // Maximum ±40 seconds per 50m segment for much larger variations
        $scaledSubKmVariation = ($paceVariability / 100.0) * $maxSubKmVariationSeconds;
        $subKmVariationSeconds = (mt_rand(-100, 100) / 100.0) * $scaledSubKmVariation;
        $subKmVariation = $subKmVariationSeconds / 60.0; // Convert to minutes

        // Interpolate between current and next kilometer pace
        $interpolatedPace = $currentPace + ($nextPace - $currentPace) * $positionInKm;

        // Add sub-kilometer variation
        $finalPace = $interpolatedPace + $subKmVariation;

        // Reset random seed to avoid affecting other random operations
        mt_srand();

        return $finalPace;
    }

    /**
     * Calculate distance between two points using Haversine formula
     *
     * @param float $lat1 Latitude of point 1 in degrees
     * @param float $lng1 Longitude of point 1 in degrees
     * @param float $lat2 Latitude of point 2 in degrees
     * @param float $lng2 Longitude of point 2 in degrees
     * @return float Distance in meters
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        // Earth's radius in meters
        $earthRadius = 6371000;

        // Convert degrees to radians
        $lat1Rad = deg2rad($lat1);
        $lng1Rad = deg2rad($lng1);
        $lat2Rad = deg2rad($lat2);
        $lng2Rad = deg2rad($lng2);

        // Calculate differences
        $latDelta = $lat2Rad - $lat1Rad;
        $lngDelta = $lng2Rad - $lng1Rad;

        // Haversine formula
        $a = sin($latDelta/2) * sin($latDelta/2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($lngDelta/2) * sin($lngDelta/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        // Distance in meters
        return $earthRadius * $c;
    }

    /**
     * Format pace to the correct format for GPX files
     *
     * @param float $pace Pace in minutes per kilometer
     * @return float Pace in minutes per kilometer (for internal use)
     */
    private function formatPaceForGpx(float $pace): float
    {
        // Validate pace input
        if ($pace <= 0) {
            $pace = 5.0; // Default to 5 min/km if invalid
        }

        // Apply realistic limits (world record is ~2:50 min/km, typical slowest is ~15 min/km)
        $minPace = 2.83; // 2:50 min/km (elite runner pace)
        $maxPace = 15.0; // 15:00 min/km (very slow walking pace)

        $pace = min(max($pace, $minPace), $maxPace);

        return $pace;
    }

    /**
     * Sanitize filename
     *
     * @param string $name
     * @return string
     */
    private function sanitizeFileName(string $name): string
    {
        return preg_replace('/[^a-z0-9]/i', '_', $name);
    }
}

