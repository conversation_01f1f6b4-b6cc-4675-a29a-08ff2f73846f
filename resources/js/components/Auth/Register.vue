<template>
    <div class="flex justify-center">
        <div class="w-full max-w-md">
            <div class="bg-white p-8 rounded-lg shadow-md">
                <h1 class="text-2xl font-bold text-strava-orange mb-6 text-center">Create Your CreateYourRun Account</h1>

                <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                    <p>{{ error }}</p>
                </div>

                <form @submit.prevent="register">
                    <div class="mb-4">
                        <label class="block text-strava-grayLight mb-2" for="name">Full Name</label>
                        <input type="text" id="name" v-model="name" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-strava-grayLight mb-2" for="email">Email Address</label>
                        <input type="email" id="email" v-model="email" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-strava-grayLight mb-2" for="password">Password</label>
                        <input type="password" id="password" v-model="password" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-6">
                        <label class="block text-strava-grayLight mb-2" for="password_confirmation">Confirm Password</label>
                        <input type="password" id="password_confirmation" v-model="passwordConfirmation" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="terms" v-model="acceptTerms" class="mr-2 accent-strava-orange" required>
                            <label for="terms" class="text-strava-grayMedium">I agree to the <a href="#" class="text-strava-orange hover:underline">Terms of Service</a> and <a href="#" class="text-strava-orange hover:underline">Privacy Policy</a></label>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">
                        {{ loading ? 'Creating Account...' : 'Create Account' }}
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-strava-grayLight">Already have an account?
                        <router-link :to="{ name: 'login' }" class="text-strava-orange hover:underline">Login</router-link>
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { getUser, setUser } from '../../utils/auth';

export default {
    name: 'Register',
    setup() {
        const router = useRouter();
        const name = ref('');
        const email = ref('');
        const password = ref('');
        const passwordConfirmation = ref('');
        const acceptTerms = ref(false);
        const loading = ref(false);
        const error = ref('');
        
        // Check if user is already logged in
        if (getUser()) {
            router.push({ name: 'home' });
            return;
        }

        const register = async () => {
            loading.value = true;
            error.value = '';

            // Basic validation
            if (password.value !== passwordConfirmation.value) {
                error.value = 'Passwords do not match';
                loading.value = false;
                return;
            }

            try {
                const response = await axios.post('/api/register', {
                    name: name.value,
                    email: email.value,
                    password: password.value,
                    password_confirmation: passwordConfirmation.value
                });

                const { user, token } = response.data;
                
                // Store the auth token and user info using our auth utility
                setUser(user, token);

                // Redirect to the home page
                router.push({ name: 'home' });
            } catch (err) {
                if (err.response && err.response.data && err.response.data.errors) {
                    const errors = err.response.data.errors;
                    error.value = Object.values(errors)[0][0]; // Get first error message
                } else {
                    error.value = 'Registration failed. Please try again.';
                }
            } finally {
                loading.value = false;
            }
        };

        return {
            name,
            email,
            password,
            passwordConfirmation,
            acceptTerms,
            loading,
            error,
            register
        };
    }
}
</script>
