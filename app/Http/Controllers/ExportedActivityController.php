<?php

namespace App\Http\Controllers;

use App\Models\ExportedActivity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ExportedActivityController extends Controller
{
    /**
     * Display a listing of the user's exported activities.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $exportedActivities = Auth::user()->exportedActivities()
            ->with('activity')
            ->latest()
            ->get();
            
        return response()->json($exportedActivities);
    }

    /**
     * Display the specified exported activity.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $exportedActivity = Auth::user()->exportedActivities()
            ->with('activity')
            ->findOrFail($id);
            
        return response()->json($exportedActivity);
    }
    
    /**
     * Download an exported activity file.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function download($id)
    {
        $exportedActivity = Auth::user()->exportedActivities()->findOrFail($id);
        
        $headers = [
            'Content-Type' => 'application/gpx+xml',
            'Content-Disposition' => 'attachment; filename="' . $exportedActivity->file_name . '"',
        ];
        
        return response($exportedActivity->export_data, 200, $headers);
    }
    
    /**
     * Store a new exported activity record.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'activity_id' => 'required|integer|exists:activities,id',
            'format' => 'required|string|in:gpx,tcx,csv',
        ]);
        
        $user = Auth::user();
        
        // Check if user has tokens
        if (!$user->hasAvailableTokens()) {
            return response()->json([
                'message' => 'No tokens available for export'
            ], 403);
        }
        
        // Create the exported activity record
        $exportedActivity = new ExportedActivity();
        $exportedActivity->user_id = $user->id;
        $exportedActivity->activity_id = $request->activity_id;
        $exportedActivity->format = $request->format;
        $exportedActivity->file_name = $request->input('file_name', 'activity.' . $request->format);
        $exportedActivity->export_data = $request->input('export_data', ''); // Optional
        $exportedActivity->save();
        
        return response()->json([
            'message' => 'Activity export logged successfully',
            'exported_activity' => $exportedActivity
        ], 201);
    }
}
