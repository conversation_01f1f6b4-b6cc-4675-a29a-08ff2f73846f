<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('basic setup works', function () {
    $user = User::factory()->create();
    
    expect($user)->toBeInstanceOf(User::class);
    expect($user->tokens)->toBeInt();
});

test('user can have tokens', function () {
    $user = User::factory()->withManyTokens()->create();
    
    expect($user->tokens)->toBeGreaterThan(0);
    expect($user->hasAvailableTokens())->toBeTrue();
});

test('user without tokens', function () {
    $user = User::factory()->withoutTokens()->create();
    
    expect($user->tokens)->toBe(0);
    expect($user->hasAvailableTokens())->toBeFalse();
});
