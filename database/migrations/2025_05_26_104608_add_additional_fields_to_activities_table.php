<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAdditionalFieldsToActivitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('activities', function (Blueprint $table) {
            $table->decimal('avg_heart_rate', 5, 2)->nullable()->after('gpx_data');
            $table->integer('cadence')->nullable()->after('avg_heart_rate');
            $table->decimal('distance', 8, 2)->nullable()->after('cadence');
            $table->decimal('pace_variability', 5, 2)->default(5.0)->after('distance'); // Pace variability percentage (0-100)
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('activities', function (Blueprint $table) {
            $table->dropColumn(['avg_heart_rate', 'cadence', 'distance', 'pace_variability']);
            
        });
    }
}
