<?php

use App\Http\Controllers\ActivityExportController;
use Illuminate\Http\Request;

it('compensates for Strava timezone conversion by subtracting offset', function () {
    $controller = new ActivityExportController();

    // Test data - user wants 8:00 AM on May 28, 2025 (without timezone specified)
    $requestData = [
        'name' => 'Morning Run',
        'routePoints' => [
            [40.7128, -74.0060], // New York coordinates
            [40.7130, -74.0062]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);

    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();

    $gpxContent = $responseData->content;

    // The GPX should contain time with UTC+7 offset subtracted (01:00 AM UTC)
    // So when Strava adds UTC+7 back, it shows 8:00 AM as intended
    expect($gpxContent)->toContain('2025-05-28T01:');
});

it('handles timezone parameter correctly when provided', function () {
    $controller = new ActivityExportController();

    // Test data with timezone - user in Vietnam (UTC+7) wants 8:00 AM local time
    $requestData = [
        'name' => 'Morning Run with Timezone',
        'routePoints' => [
            [16.0707, 108.2362], // Vietnam coordinates
            [16.0708, 108.2363]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000,
        'timezone' => 'Asia/Ho_Chi_Minh' // UTC+7 timezone
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);

    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();

    $gpxContent = $responseData->content;

    // With timezone provided, we subtract the UTC+7 offset (01:00 AM UTC)
    // So when Strava adds UTC+7 back, it shows 8:00 AM as intended
    expect($gpxContent)->toContain('2025-05-28T01:');
});

it('handles invalid timezone gracefully', function () {
    $controller = new ActivityExportController();

    // Test data with invalid timezone
    $requestData = [
        'name' => 'Morning Run with Invalid Timezone',
        'routePoints' => [
            [40.7128, -74.0060],
            [40.7130, -74.0062]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800,
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000,
        'timezone' => 'Invalid/Timezone'
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);

    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();

    // Should fallback to default UTC+7 offset (01:00 AM UTC for 8:00 AM local)
    $gpxContent = $responseData->content;
    expect($gpxContent)->toContain('2025-05-28T01:');
});

it('fixes the specific user scenario: May 27 2025 at 19:00 should show correctly in Strava', function () {
    $controller = new ActivityExportController();

    // Test the exact scenario from the user's issue
    $requestData = [
        'name' => 'Test Run for Time Verification',
        'routePoints' => [
            [16.07687, 108.241122], // Vietnam coordinates from the user's GPX
            [16.076865, 108.240976]
        ],
        'activityDate' => '2025-05-27',  // Tuesday, May 27, 2025
        'activityTime' => '19:00',       // 7:00 PM
        'duration' => 1800, // 30 minutes
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);

    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();

    $gpxContent = $responseData->content;

    // The GPX should contain 12:00 UTC (19:00 - 7 hours)
    // So when Strava adds UTC+7 back, it shows 19:00 (7:00 PM) on May 27, 2025
    // NOT 2:00 AM on May 28, 2025
    expect($gpxContent)->toContain('2025-05-27T12:');
});