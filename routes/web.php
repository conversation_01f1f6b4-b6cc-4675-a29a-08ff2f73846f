<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/login', function () {
    return view('welcome');
})->name('login');

// PayOS return URLs
Route::get('/payment/success', function () {
    return view('welcome');
})->name('payment.success');

Route::get('/payment/cancel', function () {
    return view('welcome');
})->name('payment.cancel');

Route::get('/{any?}', function () {
    return view('welcome');
})->where('any', '.*');
