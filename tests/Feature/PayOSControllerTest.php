<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\PaymentTransaction;
use Laravel\Sanctum\Sanctum;

class PayOSControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'tokens' => 5
        ]);
    }

    public function test_create_payment_link_requires_authentication()
    {
        $response = $this->postJson('/api/payos/create-payment', [
            'package_type' => 'basic'
        ]);

        $response->assertStatus(401);
    }

    public function test_create_payment_link_validates_package_type()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/payos/create-payment', [
            'package_type' => 'invalid'
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid package type'
                ]);
    }

    public function test_create_payment_link_creates_transaction_record()
    {
        Sanctum::actingAs($this->user);

        // Mock PayOS to avoid actual API calls in tests
        $this->mockPayOSResponse();

        $response = $this->postJson('/api/payos/create-payment', [
            'package_type' => 'basic'
        ]);

        $response->assertStatus(200);

        // Check that a payment transaction was created
        $this->assertDatabaseHas('payment_transactions', [
            'user_id' => $this->user->id,
            'package_type' => 'basic',
            'status' => PaymentTransaction::STATUS_PENDING
        ]);
    }

    public function test_get_payment_status_requires_authentication()
    {
        $response = $this->getJson('/api/payos/payment-status/123456');

        $response->assertStatus(401);
    }

    public function test_get_payment_status_returns_transaction_data()
    {
        Sanctum::actingAs($this->user);

        $transaction = PaymentTransaction::create([
            'user_id' => $this->user->id,
            'payos_order_code' => '123456',
            'package_type' => 'basic',
            'amount' => 499,
            'tokens' => 5,
            'status' => PaymentTransaction::STATUS_PENDING,
            'description' => 'Test transaction'
        ]);

        $response = $this->getJson('/api/payos/payment-status/123456');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'orderCode' => '123456',
                        'status' => PaymentTransaction::STATUS_PENDING,
                        'amount' => 499,
                        'tokens' => 5,
                        'package_type' => 'basic'
                    ]
                ]);
    }

    public function test_webhook_processes_payment_completion()
    {
        $transaction = PaymentTransaction::create([
            'user_id' => $this->user->id,
            'payos_order_code' => '123456',
            'package_type' => 'basic',
            'amount' => 499,
            'tokens' => 5,
            'status' => PaymentTransaction::STATUS_PENDING,
            'description' => 'Test transaction'
        ]);

        $webhookData = [
            'data' => [
                'orderCode' => '123456',
                'status' => 'PAID',
                'amount' => 499
            ]
        ];

        $response = $this->postJson('/api/payos/webhook', $webhookData);

        $response->assertStatus(200);

        // Check that transaction was marked as paid
        $transaction->refresh();
        $this->assertEquals(PaymentTransaction::STATUS_PAID, $transaction->status);
        $this->assertNotNull($transaction->paid_at);

        // Check that tokens were added to user
        $this->user->refresh();
        $this->assertEquals(10, $this->user->tokens); // 5 original + 5 from transaction
    }

    private function mockPayOSResponse()
    {
        // In a real test, you would mock the PayOS service
        // For now, we'll assume the controller handles the PayOS integration
        // and focus on testing the application logic
    }
}
