<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PaymentTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'payos_order_code',
        'payos_payment_link_id',
        'package_type',
        'amount',
        'tokens',
        'status',
        'description',
        'payos_response',
        'paid_at',
        'cancelled_at',
        'cancellation_reason'
    ];

    protected $casts = [
        'payos_response' => 'array',
        'paid_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'amount' => 'integer',
        'tokens' => 'integer'
    ];

    const STATUS_PENDING = 'PENDING';
    const STATUS_PAID = 'PAID';
    const STATUS_CANCELLED = 'CANCELLED';
    const STATUS_EXPIRED = 'EXPIRED';

    /**
     * Get the user that owns the payment transaction.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the transaction is paid.
     */
    public function isPaid()
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * Check if the transaction is pending.
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the transaction is cancelled.
     */
    public function isCancelled()
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Mark the transaction as paid.
     */
    public function markAsPaid()
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'paid_at' => Carbon::now()
        ]);
    }

    /**
     * Mark the transaction as cancelled.
     */
    public function markAsCancelled($reason = null)
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'cancelled_at' => Carbon::now(),
            'cancellation_reason' => $reason
        ]);
    }

    /**
     * Generate a unique order code for PayOS.
     */
    public static function generateOrderCode()
    {
        return intval(substr(strval(microtime(true) * 10000), -6));
    }

    /**
     * Get package configuration.
     */
    public function getPackageConfig()
    {
        return config("payos.packages.{$this->package_type}");
    }
}
