<template>
    <div class="flex justify-center">
        <div class="w-full max-w-md">
            <div class="bg-white p-8 rounded-lg shadow-md">
                <h1 class="text-2xl font-bold text-strava-orange mb-6 text-center">Login to CreateYourRun</h1>

                <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                    <p>{{ error }}</p>
                </div>

                <form @submit.prevent="login">
                    <div class="mb-4">
                        <label class="block text-strava-grayLight mb-2" for="email">Email Address</label>
                        <input type="email" id="email" v-model="email" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-6">
                        <label class="block text-strava-grayLight mb-2" for="password">Password</label>
                        <input type="password" id="password" v-model="password" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="remember" v-model="remember" class="mr-2 accent-strava-orange">
                            <label for="remember" class="text-strava-grayMedium">Remember Me</label>
                        </div>

                        <a href="#" class="text-strava-orange hover:underline">Forgot Password?</a>
                    </div>

                    <button type="submit" class="w-full bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">
                        {{ loading ? 'Logging in...' : 'Login' }}
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-strava-grayLight">Don't have an account?
                        <router-link :to="{ name: 'register' }" class="text-strava-orange hover:underline">Register</router-link>
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { getUser, setUser } from '../../utils/auth';

export default {
    name: 'Login',
    setup() {
        const router = useRouter();
        const email = ref('');
        const password = ref('');
        const remember = ref(false);
        const loading = ref(false);
        const error = ref('');
        
        // Check if user is already logged in
        if (getUser()) {
            router.push({ name: 'home' });
            return;
        }

        const login = async () => {
            loading.value = true;
            error.value = '';

            try {
                // Make a real API call to the backend for authentication
                const response = await axios.post('/api/login', {
                    email: email.value,
                    password: password.value,
                    remember: remember.value
                });

                // Get user data and token from the response
                const { user, token } = response.data;

                // Store the authentication data using our auth utility
                setUser(user, token);

                // Redirect to the home page
                router.push({ name: 'home' });
            } catch (err) {
                if (err.response && err.response.data && err.response.data.errors) {
                    // Get specific error message from the API response
                    const errorData = err.response.data.errors;
                    error.value = errorData.email ? errorData.email[0] : 'Login failed. Please check your credentials.';
                } else {
                    error.value = 'Login failed. Please check your credentials and try again.';
                }
                console.error('Login error:', err);
            } finally {
                loading.value = false;
            }
        };

        return {
            email,
            password,
            remember,
            loading,
            error,
            login
        };
    }
}
</script>
