import axios from 'axios';

// Create a custom event for auth state changes
export const authEvents = new EventTarget();

// Setup axios interceptor to add auth token to all requests
axios.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('createyourrun_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Setup axios interceptor to handle auth errors
axios.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            // Clear auth data on 401 Unauthorized response
            localStorage.removeItem('createyourrun_token');
            localStorage.removeItem('createyourrun_user');
            // Redirect to login page if needed
            if (window.router) {
                window.router.push({ name: 'login' });
            }
        }
        return Promise.reject(error);
    }
);

// Auth utility functions
export const getUser = () => {
    const userString = localStorage.getItem('createyourrun_user');
    return userString ? JSON.parse(userString) : null;
};

export const getAuthToken = () => {
    return localStorage.getItem('createyourrun_token');
};

export const setUser = (user, token) => {
    localStorage.setItem('createyourrun_user', JSON.stringify(user));
    localStorage.setItem('createyourrun_token', token);
    
    // Dispatch auth state changed event
    authEvents.dispatchEvent(new CustomEvent('auth-state-changed'));
};

export const logout = async () => {
    try {
        // Call the logout endpoint if it exists
        if (getAuthToken()) {
            await axios.post('/api/logout');
        }
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        // Always clear local storage data
        localStorage.removeItem('createyourrun_token');
        localStorage.removeItem('createyourrun_user');
        delete axios.defaults.headers.common['Authorization'];
        
        // Dispatch auth state changed event
        authEvents.dispatchEvent(new CustomEvent('auth-state-changed'));
        
        // Return to login page
        if (window.router) {
            window.router.push({ name: 'login' });
        }
    }
};
