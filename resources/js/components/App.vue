<template>
    <div class="flex flex-col min-h-screen bg-strava-surface">
        <nav class="bg-white text-strava-gray border-b border-gray-200 shadow-sm">
            <div class="container mx-auto px-4 py-3 flex justify-between items-center">
                <router-link :to="{ name: 'home' }" class="text-xl font-bold text-strava-orange">CreateYourRun</router-link>
                <div class="space-x-6">
                    <router-link :to="{ name: 'generate-activity' }" class="text-strava-grayMedium hover:text-strava-orange font-medium">Generate Activity</router-link>
                    <router-link :to="{ name: 'export-activity' }" class="text-strava-grayMedium hover:text-strava-orange font-medium">Export</router-link>
                    <!-- Authentication Links -->
                    <template v-if="user">
                        <router-link :to="{ name: 'purchase-tokens' }" class="text-strava-grayMedium hover:text-strava-orange font-medium">Get Tokens</router-link>
                        <router-link :to="{ name: 'profile' }" class="text-strava-grayMedium hover:text-strava-orange font-medium">{{ user.name }}</router-link>
                        <button @click="handleLogout" class="text-strava-grayMedium hover:text-strava-orange font-medium">Logout</button>
                    </template>
                    <template v-else>
                        <router-link :to="{ name: 'login' }" class="text-strava-grayMedium hover:text-strava-orange font-medium">Login</router-link>
                        <router-link :to="{ name: 'register' }" class="text-strava-grayMedium hover:text-strava-orange font-medium">Register</router-link>
                    </template>
                </div>
            </div>
        </nav>

        <main class="flex-grow container mx-auto px-4 py-8">
            <router-view></router-view>
        </main>

        <footer class="bg-white py-6 border-t border-gray-200 mt-auto">
            <div class="container mx-auto px-4 text-center text-strava-grayLight">
                <p>©  CreateYourRun. All rights reserved.</p>
            </div>
        </footer>
    </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue';
import { getUser, logout, authEvents } from '../utils/auth';

export default {
    name: 'App',
    setup() {
        const user = ref(null);

        const loadUser = () => {
            user.value = getUser();
        };

        const handleLogout = async () => {
            await logout();
            user.value = null;
        };
        
        // Handle auth state changes
        const handleAuthStateChanged = () => {
            loadUser();
        };

        // Initialize user data
        onMounted(() => {
            loadUser();
            
            // Listen for authentication events from localStorage (cross-tab)
            window.addEventListener('storage', (event) => {
                if (event.key === 'createyourrun_user') {
                    loadUser();
                }
            });
            
            // Listen for our custom auth state change events (same-tab)
            authEvents.addEventListener('auth-state-changed', handleAuthStateChanged);
        });
        
        onUnmounted(() => {
            // Clean up event listener
            authEvents.removeEventListener('auth-state-changed', handleAuthStateChanged);
        });

        return {
            user,
            handleLogout
        };
    }
}
</script>
