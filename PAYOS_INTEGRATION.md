# PayOS Integration Guide

This document provides instructions for setting up and using the PayOS payment gateway integration in the FakeMyRun Laravel application.

## Overview

PayOS is a Vietnamese payment gateway that supports various payment methods including VietQR, bank transfers, and digital wallets. This integration allows users to purchase token packages using PayOS's hosted payment page.

## Prerequisites

1. PayOS account with API credentials
2. <PERSON><PERSON> application with Sanctum authentication
3. Vue.js frontend with Axios for API calls

## Setup Instructions

### 1. Environment Configuration

Add the following environment variables to your `.env` file:

```env
PAYOS_CLIENT_ID=your_client_id_here
PAYOS_API_KEY=your_api_key_here
PAYOS_CHECKSUM_KEY=your_checksum_key_here
PAYOS_PARTNER_CODE=your_partner_code_here
```

**Note**: You can obtain these credentials from your PayOS merchant dashboard. For testing, you can use PayOS sandbox credentials.

### 2. Database Migration

The payment transactions table has been created with the following structure:

```sql
- id (primary key)
- user_id (foreign key to users table)
- payos_order_code (unique PayOS order identifier)
- payos_payment_link_id (PayOS payment link ID)
- package_type (basic, standard, premium)
- amount (payment amount in cents)
- tokens (number of tokens purchased)
- status (PENDING, PAID, CANCELLED, EXPIRED)
- description (payment description)
- payos_response (JSON field for PayOS API responses)
- paid_at, cancelled_at (timestamps)
- cancellation_reason (text field)
- created_at, updated_at (timestamps)
```

### 3. Package Configuration

Token packages are configured in `config/payos.php`:

- **Basic**: 5 tokens for $4.99
- **Standard**: 15 tokens for $9.99
- **Premium**: 30 tokens for $14.99

You can modify these packages by editing the configuration file.

## API Endpoints

### Create Payment Link
```
POST /api/payos/create-payment
```

**Request Body:**
```json
{
    "package_type": "basic|standard|premium"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "checkoutUrl": "https://payos.vn/checkout/...",
        "orderCode": "123456",
        "amount": 499,
        "tokens": 5,
        "package": {...}
    }
}
```

### Get Payment Status
```
GET /api/payos/payment-status/{orderCode}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "orderCode": "123456",
        "status": "PENDING|PAID|CANCELLED",
        "amount": 499,
        "tokens": 5,
        "package_type": "basic",
        "created_at": "2024-01-01T00:00:00Z",
        "paid_at": null
    }
}
```

### Cancel Payment
```
POST /api/payos/cancel-payment/{orderCode}
```

**Request Body:**
```json
{
    "reason": "User cancelled payment"
}
```

### Webhook Endpoint
```
POST /api/payos/webhook
```

This endpoint receives payment status updates from PayOS and automatically updates transaction status and user token balance.

## Frontend Integration

The `PurchaseTokens.vue` component has been updated to integrate with PayOS:

1. **Package Selection**: Users can select from available token packages
2. **Payment Creation**: Creates a PayOS payment link via API
3. **Hosted Checkout**: Redirects users to PayOS secure payment page
4. **Payment Handling**: Processes payment success/failure via return URLs
5. **Token Update**: Automatically updates user token balance after successful payment

### Key Features:

- **Hosted Payment Page**: Users are redirected to PayOS secure payment page
- **Real-time Status Updates**: Payment status is updated via webhooks
- **Error Handling**: Comprehensive error handling for failed payments
- **User Feedback**: Clear success/error messages for users
- **URL Parameter Handling**: Processes payment results from return URLs

## Security Considerations

1. **Webhook Signature Verification**: All webhook requests are verified using HMAC-SHA256
2. **User Authentication**: All payment endpoints require user authentication
3. **Transaction Validation**: Payment amounts and user ownership are validated
4. **Environment Variables**: Sensitive PayOS credentials are stored in environment variables

## Testing

Run the PayOS integration tests:

```bash
php artisan test tests/Feature/PayOSControllerTest.php
```

The test suite covers:
- Authentication requirements
- Package validation
- Transaction creation
- Payment status retrieval
- Webhook processing
- Token balance updates

## Troubleshooting

### Common Issues:

1. **PayOS Script Not Loading**: Ensure the PayOS JavaScript SDK is loaded before initializing checkout
2. **Webhook Signature Verification Failed**: Check that the checksum key matches your PayOS account
3. **Payment Link Creation Failed**: Verify API credentials and network connectivity
4. **Tokens Not Added**: Check webhook processing and database transaction logs

### Debugging:

Check Laravel logs for detailed error information:
```bash
tail -f storage/logs/laravel.log
```

PayOS integration events are logged with context for debugging.

## Production Deployment

1. Set PayOS environment to production in configuration
2. Configure webhook URL to point to your production domain
3. Test payment flow with small amounts before going live
4. Monitor logs for any integration issues
5. Set up monitoring for failed webhook deliveries

## Support

For PayOS-specific issues, refer to the official PayOS documentation or contact their support team.
For application-specific issues, check the Laravel logs and test suite results.
