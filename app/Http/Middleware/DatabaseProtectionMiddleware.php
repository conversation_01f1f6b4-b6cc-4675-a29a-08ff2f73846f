<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseProtectionMiddleware
{
    /**
     * Handle an incoming request and log database operations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Log critical database operations
        if (app()->environment('production')) {
            $this->logDatabaseOperation($request);
        }
        
        // Verify we're not accidentally using test database in production
        $this->verifyDatabaseEnvironment();
        
        return $next($request);
    }
    
    /**
     * Log database operations for audit trail
     */
    private function logDatabaseOperation(Request $request)
    {
        $user = $request->user();
        $userId = $user ? $user->id : 'anonymous';
        
        Log::channel('database')->info('Database operation', [
            'user_id' => $userId,
            'route' => $request->route()?->getName(),
            'method' => $request->method(),
            'url' => $request->url(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString(),
            'database' => DB::connection()->getDatabaseName()
        ]);
    }
    
    /**
     * Verify we're using the correct database for the environment
     */
    private function verifyDatabaseEnvironment()
    {
        $environment = app()->environment();
        $databaseName = DB::connection()->getDatabaseName();
        
        // Prevent test database usage in production
        if ($environment === 'production' && 
            (str_contains($databaseName, 'test') || 
             str_contains($databaseName, 'testing') ||
             $databaseName === ':memory:')) {
            
            Log::emergency('CRITICAL: Production environment using test database', [
                'environment' => $environment,
                'database' => $databaseName,
                'timestamp' => now()->toISOString()
            ]);
            
            abort(500, 'Database configuration error detected');
        }
        
        // Prevent production database usage in testing
        if ($environment === 'testing' && 
            !str_contains($databaseName, 'test') && 
            $databaseName !== ':memory:') {
            
            Log::emergency('CRITICAL: Test environment using production database', [
                'environment' => $environment,
                'database' => $databaseName,
                'timestamp' => now()->toISOString()
            ]);
            
            abort(500, 'Test environment database configuration error');
        }
    }
}
