<?php

namespace Database\Factories;

use App\Models\Activity;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ActivityFactory extends Factory
{
    protected $model = Activity::class;

    public function definition()
    {
        // Generate realistic route points for testing
        $routePoints = $this->generateRoutePoints();

        return [
            'user_id' => User::factory(),
            'name' => $this->faker->words(3, true) . ' Run',
            'route_points' => $routePoints,
            'activity_date' => $this->faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d'),
            'activity_time' => $this->faker->time('H:i:s'),
            'duration' => $this->faker->numberBetween(1800, 7200), // 30 minutes to 2 hours
            'pace' => $this->faker->randomFloat(2, 4.0, 8.0), // 4-8 min/km
            'pace_variability' => $this->faker->randomFloat(1, 0.0, 20.0), // 0-20% pace variability
            'activity_type' => $this->faker->randomElement(['running', 'cycling', 'hiking']),
            'distance' => $this->faker->numberBetween(5000, 25000), // 5-25km in meters
            'avg_heart_rate' => $this->faker->numberBetween(120, 180),
            'cadence' => $this->faker->numberBetween(160, 190),
        ];
    }

    /**
     * Generate realistic GPS route points for testing
     */
    private function generateRoutePoints(): array
    {
        $points = [];
        $pointCount = $this->faker->numberBetween(10, 50);

        // Start with a base location (San Francisco area)
        $baseLat = 37.7749;
        $baseLng = -122.4194;

        for ($i = 0; $i < $pointCount; $i++) {
            // Generate points in a small area to simulate a realistic route
            $latOffset = $this->faker->randomFloat(6, -0.01, 0.01);
            $lngOffset = $this->faker->randomFloat(6, -0.01, 0.01);

            $points[] = [
                $baseLat + $latOffset,
                $baseLng + $lngOffset
            ];
        }

        return $points;
    }

    /**
     * Create an activity with running-specific data
     */
    public function running()
    {
        return $this->state(function (array $attributes) {
            return [
                'activity_type' => 'running',
                'pace' => $this->faker->randomFloat(2, 4.0, 7.0), // 4-7 min/km for running
                'cadence' => $this->faker->numberBetween(170, 190), // Running cadence
                'avg_heart_rate' => $this->faker->numberBetween(140, 180),
            ];
        });
    }

    /**
     * Create an activity with cycling-specific data
     */
    public function cycling()
    {
        return $this->state(function (array $attributes) {
            return [
                'activity_type' => 'cycling',
                'pace' => $this->faker->randomFloat(2, 2.0, 4.0), // 2-4 min/km for cycling
                'cadence' => $this->faker->numberBetween(80, 100), // Cycling cadence
                'avg_heart_rate' => $this->faker->numberBetween(120, 160),
            ];
        });
    }

    /**
     * Create an activity with hiking-specific data
     */
    public function hiking()
    {
        return $this->state(function (array $attributes) {
            return [
                'activity_type' => 'hiking',
                'pace' => $this->faker->randomFloat(2, 8.0, 15.0), // 8-15 min/km for hiking
                'cadence' => $this->faker->numberBetween(120, 150), // Hiking cadence
                'avg_heart_rate' => $this->faker->numberBetween(110, 150),
            ];
        });
    }

    /**
     * Create an activity with minimal required data
     */
    public function minimal()
    {
        return $this->state(function (array $attributes) {
            return [
                'route_points' => [
                    [37.7749, -122.4194],
                    [37.7750, -122.4195],
                    [37.7751, -122.4196]
                ],
                'avg_heart_rate' => null,
                'cadence' => null,
                'distance' => null,
            ];
        });
    }

    /**
     * Create an activity with complex route data
     */
    public function complexRoute()
    {
        return $this->state(function (array $attributes) {
            $points = [];
            $pointCount = 100; // More points for complex route

            // Create a more complex route pattern
            $baseLat = 37.7749;
            $baseLng = -122.4194;

            for ($i = 0; $i < $pointCount; $i++) {
                $angle = ($i / $pointCount) * 2 * M_PI;
                $radius = 0.005 * sin($angle * 3); // Create a figure-8 pattern

                $latOffset = $radius * cos($angle);
                $lngOffset = $radius * sin($angle);

                $points[] = [
                    $baseLat + $latOffset,
                    $baseLng + $lngOffset
                ];
            }

            return [
                'route_points' => $points,
                'duration' => 3600, // 1 hour
                'distance' => 10000, // 10km
            ];
        });
    }
}
