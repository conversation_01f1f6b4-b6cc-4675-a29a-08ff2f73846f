<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'route_points',
        'activity_date',
        'activity_time',
        'duration',
        'pace',
        'pace_variability',
        'activity_type',
        'gpx_data',
        'avg_heart_rate',
        'cadence',
        'distance'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'route_points' => 'json',
        'activity_date' => 'date:Y-m-d',
        'duration' => 'float',
        'pace' => 'float',
        'pace_variability' => 'float',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['formatted_pace'];

    /**
     * Get the user that owns the activity.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the exports for this activity.
     */
    public function exports()
    {
        return $this->hasMany(ExportedActivity::class);
    }

    /**
     * Format pace from decimal to MM:SS format
     *
     * @param float $pace Pace in minutes per kilometer
     * @return string Formatted pace in MM:SS min/km format
     */
    public static function formatPaceDisplay(float $pace): string
    {
        $minutes = floor($pace);
        $seconds = floor(($pace - $minutes) * 60);
        return sprintf('%d:%02d min/km', $minutes, $seconds);
    }

    /**
     * Get the formatted pace attribute
     *
     * @return string
     */
    public function getFormattedPaceAttribute(): string
    {
        return self::formatPaceDisplay($this->pace);
    }
}
