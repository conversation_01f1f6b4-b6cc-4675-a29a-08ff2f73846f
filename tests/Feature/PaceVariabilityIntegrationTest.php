<?php

use App\Models\Activity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->withManyTokens()->create();
});

it('implements complete pace variability workflow', function () {
    Sanctum::actingAs($this->user);

    // Test data with specific pace variability
    $testData = [
        'name' => 'Pace Variability Integration Test',
        'routePoints' => [
            [37.7749, -122.4194],
            [37.7750, -122.4195],
            [37.7751, -122.4196],
            [37.7752, -122.4197],
            [37.7753, -122.4198]
        ],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.0, // 5:00 min/km
        'paceVariability' => 20.0, // 20% variability
        'distance' => 6000, // 6km
        'includeHeartRate' => true,
        'heartRate' => 155,
        'includeElevation' => true,
        'includeCadence' => true,
        'cadence' => 175
    ];

    // 1. Test GPX export with pace variability
    $response = $this->postJson('/api/export/gpx', $testData);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'filename',
            'content',
            'mimeType',
            'tokens_remaining',
            'pace_display'
        ]);

    // 2. Verify activity was created with pace variability
    $activity = Activity::where('user_id', $this->user->id)
        ->where('name', 'Pace Variability Integration Test')
        ->first();

    expect($activity)->not->toBeNull();
    expect($activity->pace_variability)->toBe(20.0);
    expect($activity->pace)->toBe(5.0);

    // 3. Verify GPX content includes speed variations
    $gpxContent = $response->json('content');

    // Parse XML and extract speed values
    $xml = simplexml_load_string($gpxContent);
    $xml->registerXPathNamespace('gpxtpx', 'http://www.garmin.com/xmlschemas/TrackPointExtension/v2');
    $speeds = $xml->xpath('//gpxtpx:speed');

    expect(count($speeds))->toBeGreaterThan(0);

    if (count($speeds) > 2) {
        // Convert to array of floats
        $speedValues = array_map('floatval', $speeds);

        // Calculate base speed from pace (5.0 min/km = 3.33 m/s)
        $expectedBaseSpeed = 1000.0 / (5.0 * 60.0); // ≈ 3.33 m/s

        // With 20% variability, speeds should range from ~2.67 to ~4.0 m/s
        $minExpectedSpeed = $expectedBaseSpeed * 0.8;
        $maxExpectedSpeed = $expectedBaseSpeed * 1.2;

        // Check that we have speeds within the expected range
        $speedsInRange = array_filter($speedValues, function($speed) use ($minExpectedSpeed, $maxExpectedSpeed) {
            return $speed >= $minExpectedSpeed && $speed <= $maxExpectedSpeed;
        });

        expect(count($speedsInRange))->toBeGreaterThan(0);

        // Calculate standard deviation to verify variation exists
        $mean = array_sum($speedValues) / count($speedValues);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $speedValues)) / count($speedValues);
        $stdDev = sqrt($variance);

        // With 20% variability, we should see some variation (realistic but not extreme)
        expect($stdDev)->toBeGreaterThan(0.01);
    }

    // 4. Verify GPX structure is valid
    expect($gpxContent)->toContain('<?xml version="1.0"')
        ->toContain('<gpx')
        ->toContain('<metadata>')
        ->toContain('<trk>')
        ->toContain('<trkseg>')
        ->toContain('<trkpt')
        ->toContain('<gpxtpx:speed>')
        ->toContain('</gpx>');

    // 5. Test that token was consumed
    $this->user->refresh();
    expect($this->user->tokens)->toBeLessThan(97); // Started with 97 tokens
});

it('handles edge cases for pace variability', function () {
    Sanctum::actingAs($this->user);

    // Test with 0% variability
    $zeroVariabilityData = [
        'name' => 'Zero Variability Test',
        'routePoints' => [
            [37.7749, -122.4194],
            [37.7750, -122.4195]
        ],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 600,
        'pace' => 6.0,
        'paceVariability' => 0.0,
        'distance' => 2000
    ];

    $response = $this->postJson('/api/export/gpx', $zeroVariabilityData);
    $response->assertStatus(200);

    // Test with maximum variability
    $maxVariabilityData = [
        'name' => 'Max Variability Test',
        'routePoints' => [
            [37.7749, -122.4194],
            [37.7750, -122.4195]
        ],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 600,
        'pace' => 6.0,
        'paceVariability' => 100.0,
        'distance' => 2000
    ];

    $response = $this->postJson('/api/export/gpx', $maxVariabilityData);
    $response->assertStatus(200);
});

it('validates pace variability bounds correctly', function () {
    Sanctum::actingAs($this->user);

    $baseData = [
        'name' => 'Validation Test',
        'routePoints' => [[37.7749, -122.4194]],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 600,
        'pace' => 6.0,
        'distance' => 2000
    ];

    // Test negative pace variability
    $negativeData = array_merge($baseData, ['paceVariability' => -10.0]);
    $response = $this->postJson('/api/export/gpx', $negativeData);
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['paceVariability']);

    // Test pace variability over 100%
    $overLimitData = array_merge($baseData, ['paceVariability' => 150.0]);
    $response = $this->postJson('/api/export/gpx', $overLimitData);
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['paceVariability']);

    // Test valid pace variability
    $validData = array_merge($baseData, ['paceVariability' => 25.0]);
    $response = $this->postJson('/api/export/gpx', $validData);
    $response->assertStatus(200);
});

it('generates realistic running patterns with enhanced pace variability', function () {
    Sanctum::actingAs($this->user);

    // Test data for a longer run to see fatigue effects
    $longRunData = [
        'name' => 'Long Run Pace Pattern Test',
        'routePoints' => [
            [37.7749, -122.4194], // Start
            [37.7750, -122.4195], // 1km
            [37.7751, -122.4196], // 2km
            [37.7752, -122.4197], // 3km
            [37.7753, -122.4198], // 4km
            [37.7754, -122.4199], // 5km
            [37.7755, -122.4200], // 6km
            [37.7756, -122.4201], // 7km
            [37.7757, -122.4202], // 8km
            [37.7758, -122.4203], // 9km
            [37.7759, -122.4204]  // 10km
        ],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 3000, // 50 minutes for 10km
        'pace' => 5.0, // 5:00 min/km base pace
        'paceVariability' => 20.0, // 20% variability for realistic patterns
        'distance' => 10000, // 10km
        'includeHeartRate' => true,
        'heartRate' => 155,
        'includeElevation' => true,
        'includeCadence' => true,
        'cadence' => 175
    ];

    $response = $this->postJson('/api/export/gpx', $longRunData);
    $response->assertStatus(200);

    $gpxContent = $response->json('content');

    // Parse XML and extract speed values with timestamps
    $xml = simplexml_load_string($gpxContent);
    $xml->registerXPathNamespace('gpxtpx', 'http://www.garmin.com/xmlschemas/TrackPointExtension/v2');
    $xml->registerXPathNamespace('gpx', 'http://www.topografix.com/GPX/1/1');
    $speeds = $xml->xpath('//gpxtpx:speed');
    $times = $xml->xpath('//gpx:time');

    expect(count($speeds))->toBeGreaterThan(5); // Should have multiple speed points

    if (count($speeds) > 5) {
        // Convert to array of floats
        $speedValues = array_map('floatval', $speeds);

        // Calculate expected base speed from pace (5.0 min/km = 3.33 m/s)
        $expectedBaseSpeed = 1000.0 / (5.0 * 60.0); // ≈ 3.33 m/s

        // Verify speeds are within realistic range for 20% variability
        $minExpectedSpeed = $expectedBaseSpeed * 0.7; // Allow for warm-up bonus
        $maxExpectedSpeed = $expectedBaseSpeed * 1.4; // Allow for fatigue effect

        foreach ($speedValues as $speed) {
            expect($speed)->toBeBetween($minExpectedSpeed, $maxExpectedSpeed);
        }

        // Calculate coefficient of variation to verify meaningful variation exists
        $mean = array_sum($speedValues) / count($speedValues);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $speedValues)) / count($speedValues);
        $stdDev = sqrt($variance);
        $coefficientOfVariation = $stdDev / $mean;

        // With 20% variability and realistic patterns, we should see some variation
        // Coefficient of variation should be between 1% and 25% (realistic range)
        expect($coefficientOfVariation)->toBeBetween(0.01, 0.25);
    }
});

it('demonstrates realistic kilometer split variations for Strava analysis', function () {
    Sanctum::actingAs($this->user);

    // Simulate a 5km run with high variability to show clear split differences
    $testData = [
        'name' => 'Strava Split Analysis Test',
        'routePoints' => [
            [37.7749, -122.4194], // Start
            [37.7750, -122.4195], // 1km mark
            [37.7751, -122.4196], // 2km mark
            [37.7752, -122.4197], // 3km mark
            [37.7753, -122.4198], // 4km mark
            [37.7754, -122.4199]  // 5km finish
        ],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 2790, // 46:30 for 5km (9:18 min/km average)
        'pace' => 9.28, // User's example pace
        'paceVariability' => 18.0, // High variability for clear differences
        'distance' => 5000, // 5km
        'includeHeartRate' => true,
        'heartRate' => 150,
        'includeElevation' => true,
        'includeCadence' => true,
        'cadence' => 170
    ];

    $response = $this->postJson('/api/export/gpx', $testData);
    $response->assertStatus(200);

    // Verify activity was created with correct pace variability
    $activity = Activity::where('user_id', $this->user->id)
        ->where('name', 'Strava Split Analysis Test')
        ->first();

    expect($activity)->not->toBeNull();
    expect($activity->pace)->toBe(9.28);
    expect($activity->pace_variability)->toBe(18.0);

    $gpxContent = $response->json('content');

    // Verify GPX contains realistic speed variations
    $xml = simplexml_load_string($gpxContent);
    $xml->registerXPathNamespace('gpxtpx', 'http://www.garmin.com/xmlschemas/TrackPointExtension/v2');
    $speeds = $xml->xpath('//gpxtpx:speed');

    if (count($speeds) > 3) {
        $speedValues = array_map('floatval', $speeds);

        // Calculate expected base speed from pace (9.28 min/km ≈ 1.79 m/s)
        $expectedBaseSpeed = 1000.0 / (9.28 * 60.0);

        // With 18% variability, we should see speeds ranging roughly from 1.47 to 2.11 m/s
        $minExpectedSpeed = $expectedBaseSpeed * 0.8;
        $maxExpectedSpeed = $expectedBaseSpeed * 1.2;

        // Verify we have speeds in different ranges (indicating split variations)
        $speedRanges = [
            'fast' => 0,    // Faster than base
            'normal' => 0,  // Around base pace
            'slow' => 0     // Slower than base
        ];

        foreach ($speedValues as $speed) {
            if ($speed > $expectedBaseSpeed * 1.02) {
                $speedRanges['fast']++;
            } elseif ($speed < $expectedBaseSpeed * 0.98) {
                $speedRanges['slow']++;
            } else {
                $speedRanges['normal']++;
            }
        }

        // We should have some variation (either fast or slow speeds, or both)
        // With 18% variability, we expect to see at least some variation
        $totalVariation = $speedRanges['fast'] + $speedRanges['slow'];
        expect($totalVariation)->toBeGreaterThanOrEqual(0); // At minimum, no error

        // Calculate coefficient of variation as additional check
        $mean = array_sum($speedValues) / count($speedValues);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $speedValues)) / count($speedValues);
        $stdDev = sqrt($variance);
        $coefficientOfVariation = $stdDev / $mean;

        // With 18% variability and sub-kilometer variations, we should see some variation
        expect($coefficientOfVariation)->toBeGreaterThan(0.001);
    }
});
