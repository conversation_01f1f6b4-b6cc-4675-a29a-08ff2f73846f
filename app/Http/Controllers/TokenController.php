<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TokenController extends Controller
{
    /**
     * Get the number of tokens for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTokens()
    {
        $user = Auth::user();
        return response()->json([
            'tokens' => $user->tokens,
            'has_available_tokens' => $user->hasAvailableTokens()
        ]);
    }

    /**
     * Add tokens to the authenticated user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addTokens(Request $request)
    {
        $request->validate([
            'tokens' => 'required|integer|min:1',
        ]);

        $user = Auth::user();
        $user->tokens += $request->tokens;
        $user->save();

        return response()->json([
            'message' => $request->tokens . ' tokens added successfully',
            'tokens' => $user->tokens
        ]);
    }
    
    /**
     * Use a token for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function useToken()
    {
        $user = Auth::user();
        
        if (!$user->hasAvailableTokens()) {
            return response()->json([
                'message' => 'No tokens available',
                'tokens' => $user->tokens
            ], 403);
        }
        
        $result = $user->useToken();
        
        if (!$result) {
            return response()->json([
                'message' => 'Failed to use token',
                'tokens' => $user->tokens
            ], 500);
        }
        
        return response()->json([
            'message' => 'Token used successfully',
            'tokens' => $user->tokens,
            'user' => $user
        ]);
    }
}
