# Mapbox Integration Setup Guide

## Overview

The ActivityGenerator component has been updated to use Mapbox services for both map tiles and routing functionality, replacing the previous OpenStreetMap implementation.

## Features

### 🗺️ **Map Tiles**
- **Mapbox Streets v11**: High-quality, detailed street maps
- **Fallback Support**: Automatically falls back to OpenStreetMap if Mapbox token is not configured
- **Optimized Performance**: Uses Mapbox's tile optimization with proper tile size and zoom offset

### 🛣️ **Routing Profiles**
- **Cycling Profile**: For bike activities - optimized for bike lanes and cycling infrastructure
- **Walking Profile**: For running and hiking activities - optimized for pedestrian paths and sidewalks

## Configuration

### 1. Get Mapbox Access Token

1. Sign up for a free account at [mapbox.com](https://www.mapbox.com/)
2. Navigate to your [Account page](https://account.mapbox.com/)
3. Copy your **Default public token** or create a new token
4. The token should start with `pk.`

### 2. Environment Configuration

Add your Mapbox access token to your environment files:

**`.env`**:
```bash
MAPBOX_ACCESS_TOKEN=pk.your_actual_mapbox_token_here
MIX_MAPBOX_ACCESS_TOKEN="${MAPBOX_ACCESS_TOKEN}"
```

**`.env.example`**:
```bash
MAPBOX_ACCESS_TOKEN=
MIX_MAPBOX_ACCESS_TOKEN="${MAPBOX_ACCESS_TOKEN}"
```

### 3. Rebuild Assets

After adding the token, rebuild your assets:

```bash
npm run dev
# or for production
npm run production
```

## API Endpoints Used

### Map Tiles
```
https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token={token}
```

### Directions API
```
https://api.mapbox.com/directions/v5/mapbox/{profile}/{coordinates}?geometries=geojson&overview=full&access_token={token}
```

**Profiles**:
- `cycling` - For bike activities
- `walking` - For running and hiking activities

## Error Handling

The implementation includes comprehensive error handling:

- **Missing Token**: Falls back to OpenStreetMap tiles and shows configuration error for routing
- **Invalid Token**: Clear error message for authentication failures
- **API Limits**: Proper handling of rate limiting and quota exceeded errors
- **Network Issues**: Graceful degradation with user-friendly error messages

## Fallback Behavior

If Mapbox access token is not configured:
- **Map Tiles**: Automatically falls back to OpenStreetMap
- **Routing**: Shows error message asking user to contact administrator
- **No Breaking Changes**: Application continues to function with reduced features

## Usage Limits

Mapbox free tier includes:
- **50,000 map loads** per month
- **100,000 requests** to Directions API per month

For production use, monitor your usage in the Mapbox dashboard.

## Security Notes

- The access token is exposed in the frontend (this is normal for public tokens)
- Use **public tokens** (starting with `pk.`) for frontend applications
- Never use **secret tokens** (starting with `sk.`) in frontend code
- Consider implementing URL restrictions for production tokens

## Troubleshooting

### Common Issues

1. **Map not loading**: Check if `MIX_MAPBOX_ACCESS_TOKEN` is set correctly
2. **Routing not working**: Verify token has Directions API access
3. **Build errors**: Run `npm run dev` after changing environment variables
4. **Token errors**: Ensure token starts with `pk.` and is valid
5. **"Invalid route coordinates" error**: See detailed solutions below

### "Invalid Route Coordinates" Error Solutions

This error occurs when Mapbox can't find a valid route between your selected points. Here's how to fix it:

#### **Root Causes & Solutions:**

1. **Points too far apart**
   - **Solution**: Place points closer together (max distance varies by region)
   - **Tip**: Add intermediate waypoints for long routes

2. **Points in inaccessible areas**
   - **Solution**: Place points on or near roads/paths suitable for your activity type
   - **For cycling**: Ensure points are near bike-accessible roads
   - **For walking/running**: Place points near sidewalks or pedestrian paths

3. **Too many waypoints**
   - **Solution**: Use 25 or fewer points (Mapbox limit)
   - **Tip**: The app now shows a warning if you exceed this limit

4. **Points over water or in restricted areas**
   - **Solution**: Avoid placing points in lakes, rivers, private property, or buildings
   - **Tip**: Use satellite view to verify point placement

5. **Wrong activity type for the terrain**
   - **Solution**: Switch between "Run", "Bike", or "Hike" based on available infrastructure
   - **Example**: Use "Run" instead of "Bike" in pedestrian-only areas

#### **Improved Error Messages:**

The app now provides specific guidance:
- "No [activity] route found between the selected points. Try placing points closer to roads or paths, or use fewer waypoints."
- "Some points are in inaccessible areas. Please place points on or near roads/paths suitable for the selected activity type."
- "Cannot create [activity] route with current points. Try: 1) Moving points closer to suitable paths, 2) Using fewer waypoints, or 3) Choosing a different activity type."

#### **Best Practices:**

1. **Start Simple**: Begin with 2-3 points for testing
2. **Check Activity Type**: Ensure it matches available infrastructure
3. **Use Map Layers**: Switch to satellite view to see terrain
4. **Follow Roads**: Place points directly on visible roads/paths
5. **Test Incrementally**: Add points one by one to identify problematic locations

### Debug Steps

1. Check browser console for error messages
2. Verify environment variables: `echo $MIX_MAPBOX_ACCESS_TOKEN`
3. Test token validity in Mapbox dashboard
4. Clear browser cache and rebuild assets
5. Try with fewer, simpler route points
6. Check if points are placed on accessible roads/paths

## Migration from OpenStreetMap

The migration is backward compatible:
- **No breaking changes** to existing functionality
- **Automatic fallback** if Mapbox is not configured
- **Same UI/UX** - users won't notice the difference
- **Enhanced routing** with activity-specific profiles

## Benefits of Mapbox

- **Better Routing**: More accurate, activity-specific route suggestions
- **Higher Quality Maps**: Professional-grade cartography
- **Better Performance**: Optimized tile delivery and caching
- **Advanced Features**: Access to additional Mapbox services if needed
