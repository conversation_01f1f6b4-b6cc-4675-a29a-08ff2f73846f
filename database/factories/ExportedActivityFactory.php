<?php

namespace Database\Factories;

use App\Models\Activity;
use App\Models\ExportedActivity;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ExportedActivityFactory extends Factory
{
    protected $model = ExportedActivity::class;

    public function definition()
    {
        $formats = ['gpx', 'tcx', 'csv'];
        $format = $this->faker->randomElement($formats);
        
        return [
            'user_id' => User::factory(),
            'activity_id' => Activity::factory(),
            'format' => $format,
            'file_name' => $this->faker->words(2, true) . '.' . $format,
            'file_path' => null,
            'export_data' => $this->generateExportData($format),
        ];
    }

    /**
     * Generate sample export data based on format
     */
    private function generateExportData(string $format): string
    {
        switch ($format) {
            case 'gpx':
                return $this->generateSampleGpx();
            case 'tcx':
                return $this->generateSampleTcx();
            case 'csv':
                return $this->generateSampleCsv();
            default:
                return '';
        }
    }

    /**
     * Generate sample GPX content for testing
     */
    private function generateSampleGpx(): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<gpx version="1.1" creator="Create My Run" xmlns="http://www.topografix.com/GPX/1/1" xmlns:gpxtpx="http://www.garmin.com/xmlschemas/TrackPointExtension/v1">
  <metadata>
    <time>' . now()->toISOString() . '</time>
    <desc>Activity exported from Garmin Connect using Create My Run</desc>
    <link href="https://create-my.run/">
      <text>Create My Run</text>
    </link>
  </metadata>
  <trk>
    <name>Test Activity</name>
    <type>running</type>
    <trkseg>
      <trkpt lat="37.7749" lon="-122.4194">
        <ele>10.0</ele>
        <time>' . now()->toISOString() . '</time>
      </trkpt>
      <trkpt lat="37.7750" lon="-122.4195">
        <ele>11.0</ele>
        <time>' . now()->addSeconds(30)->toISOString() . '</time>
      </trkpt>
    </trkseg>
  </trk>
</gpx>';
    }

    /**
     * Generate sample TCX content for testing
     */
    private function generateSampleTcx(): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<TrainingCenterDatabase xmlns="http://www.garmin.com/xmlschemas/TrainingCenterDatabase/v2">
  <Activities>
    <Activity Sport="Running">
      <Id>' . now()->toISOString() . '</Id>
      <Lap StartTime="' . now()->toISOString() . '">
        <TotalTimeSeconds>1800</TotalTimeSeconds>
        <DistanceMeters>5000</DistanceMeters>
        <Track>
          <Trackpoint>
            <Time>' . now()->toISOString() . '</Time>
            <Position>
              <LatitudeDegrees>37.7749</LatitudeDegrees>
              <LongitudeDegrees>-122.4194</LongitudeDegrees>
            </Position>
            <AltitudeMeters>10.0</AltitudeMeters>
            <HeartRateBpm>
              <Value>150</Value>
            </HeartRateBpm>
          </Trackpoint>
        </Track>
      </Lap>
    </Activity>
  </Activities>
</TrainingCenterDatabase>';
    }

    /**
     * Generate sample CSV content for testing
     */
    private function generateSampleCsv(): string
    {
        return "timestamp,latitude,longitude,elevation,heart_rate,cadence\n" .
               now()->toISOString() . ",37.7749,-122.4194,10.0,150,180\n" .
               now()->addSeconds(30)->toISOString() . ",37.7750,-122.4195,11.0,152,182\n";
    }

    /**
     * Create an exported activity with GPX format
     */
    public function gpx()
    {
        return $this->state(function (array $attributes) {
            return [
                'format' => 'gpx',
                'file_name' => $this->faker->words(2, true) . '.gpx',
                'export_data' => $this->generateSampleGpx(),
            ];
        });
    }

    /**
     * Create an exported activity with TCX format
     */
    public function tcx()
    {
        return $this->state(function (array $attributes) {
            return [
                'format' => 'tcx',
                'file_name' => $this->faker->words(2, true) . '.tcx',
                'export_data' => $this->generateSampleTcx(),
            ];
        });
    }

    /**
     * Create an exported activity with CSV format
     */
    public function csv()
    {
        return $this->state(function (array $attributes) {
            return [
                'format' => 'csv',
                'file_name' => $this->faker->words(2, true) . '.csv',
                'export_data' => $this->generateSampleCsv(),
            ];
        });
    }

    /**
     * Create an exported activity with empty export data
     */
    public function withoutData()
    {
        return $this->state(function (array $attributes) {
            return [
                'export_data' => '',
            ];
        });
    }
}
