<?php

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

uses(Tests\TestCase::class)->in('Feature');
uses(Tests\TestCase::class)->in('Unit');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

expect()->extend('toBeValidGpx', function () {
    $xml = simplexml_load_string($this->value);
    expect($xml)->not->toBeFalse('Invalid XML structure');
    expect($this->value)->toContain('<?xml version="1.0"');
    expect($this->value)->toContain('<gpx');
    expect($this->value)->toContain('</gpx>');
    return $this;
});

expect()->extend('toContainGpxTrackPoints', function () {
    expect($this->value)->toContain('<trkpt');
    expect($this->value)->toContain('lat=');
    expect($this->value)->toContain('lon=');
    return $this;
});

expect()->extend('toHaveValidPaceFormat', function () {
    expect($this->value)->toMatch('/^\d+:\d{2} min\/km$/');
    return $this;
});

expect()->extend('toBeBetween', function ($min, $max) {
    expect($this->value)->toBeGreaterThanOrEqual($min);
    expect($this->value)->toBeLessThanOrEqual($max);
    return $this;
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

/**
 * Create a sample route with specified number of points
 */
function createSampleRoute(int $pointCount = 5): array
{
    $points = [];
    $baseLat = 37.7749;
    $baseLng = -122.4194;

    for ($i = 0; $i < $pointCount; $i++) {
        $points[] = [
            $baseLat + ($i * 0.001),
            $baseLng + ($i * 0.001)
        ];
    }

    return $points;
}

/**
 * Create valid GPX export data for testing
 */
function createValidGpxData(array $overrides = []): array
{
    return array_merge([
        'name' => 'Test Activity',
        'routePoints' => createSampleRoute(),
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 1800,
        'pace' => 5.5,
        'distance' => 5000,
        'includeHeartRate' => true,
        'heartRate' => 150,
        'includeElevation' => true,
        'includeCadence' => true,
        'cadence' => 180
    ], $overrides);
}

/**
 * Create valid activity data for testing
 */
function createValidActivityData(array $overrides = []): array
{
    return array_merge([
        'name' => 'Test Activity',
        'route_points' => createSampleRoute(),
        'activity_date' => '2024-01-15',
        'activity_time' => '08:30:00',
        'duration' => 1800,
        'pace' => 5.5,
        'activity_type' => 'running'
    ], $overrides);
}
