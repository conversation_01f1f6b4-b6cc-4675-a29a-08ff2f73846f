<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateActivitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('route_points')->nullable(); // JSON data for route points
            $table->date('activity_date');
            $table->time('activity_time');
            $table->decimal('duration', 10, 2); // duration in seconds
            $table->decimal('pace', 8, 2); // pace in minutes per km or mile
            $table->string('activity_type')->default('run');
            $table->text('gpx_data')->nullable(); // For storing GPX data
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('activities');
    }
}
