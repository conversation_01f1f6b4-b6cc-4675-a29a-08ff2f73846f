# Comprehensive Test Suite for FakeMyRun Application

This document describes the comprehensive PHPUnit test suite using the Pest PHP framework that has been implemented for the FakeMyRun application. The test suite focuses on ensuring the website functions correctly whenever new features are added or existing functionality is updated.

## Test Framework Setup

- **Framework**: Pest PHP v1.23.1 with Laravel integration
- **Database**: Uses RefreshDatabase trait for clean test isolation
- **Authentication**: Laravel Sanctum for API authentication testing
- **Factories**: Comprehensive model factories for test data generation

## Test Structure

### Feature Tests (`tests/Feature/`)

#### 1. ActivityExportController Tests (`ActivityExportControllerTest.php`)
**Primary Focus**: GPX file generation accuracy and route data integrity

**Key Test Areas**:
- **GPX File Generation**: Validates XML structure, namespaces, and metadata
- **Route Data Integrity**: Ensures waypoints, timestamps, and activity-specific metadata are correct
- **Token Management**: Verifies token consumption and authentication requirements
- **Activity Type Handling**: Tests different activity types (running, cycling, hiking)
- **Validation**: Comprehensive input validation and error handling
- **Content Validation**: Verifies heart rate, elevation, cadence, and speed data inclusion

**Sample Tests**:
```php
test('generates valid GPX file with correct structure')
test('includes heart rate data when requested')
test('uses a token when generating GPX')
test('validates required fields')
```

#### 2. ActivityController Tests (`ActivityControllerTest.php`)
**Primary Focus**: CRUD operations for activities

**Key Test Areas**:
- **Index**: Retrieving user activities with proper filtering and ordering
- **Store**: Creating activities with validation
- **Show**: Retrieving individual activities with authorization
- **Update**: Modifying activities with validation
- **Destroy**: Deleting activities with authorization
- **Model Behavior**: Testing casts, accessors, and relationships

#### 3. ExportedActivityController Tests (`ExportedActivityControllerTest.php`)
**Primary Focus**: Export management and file downloads

**Key Test Areas**:
- **Export Logging**: Recording export activities
- **File Downloads**: Proper headers and content delivery
- **Format Support**: GPX, TCX, and CSV format handling
- **Authorization**: User-specific export access
- **Relationships**: Activity and user associations

#### 4. TokenController Tests (`TokenControllerTest.php`)
**Primary Focus**: Token management system

**Key Test Areas**:
- **Token Retrieval**: Getting user token information
- **Token Addition**: Adding tokens with validation
- **Token Usage**: Consuming tokens with proper checks
- **Edge Cases**: Zero tokens, concurrent usage, validation

#### 5. Integration Tests (`ActivityWorkflowIntegrationTest.php`)
**Primary Focus**: Complete workflow testing

**Key Test Areas**:
- **Complete Lifecycle**: Create → Export → Download → Update → Delete
- **Activity Types**: Different workflows for running, cycling, hiking
- **Token Integration**: Token consumption across operations
- **Error Handling**: Graceful failure and data consistency

### Unit Tests (`tests/Unit/`)

#### 1. GPX Generation Tests (`GpxGenerationTest.php`)
**Primary Focus**: Low-level GPX generation validation

**Key Test Areas**:
- **XML Structure**: Valid XML with proper namespaces
- **Track Points**: Coordinate accuracy and precision
- **Timestamps**: Sequential and properly distributed timestamps
- **Extensions**: Heart rate, cadence, speed data
- **Edge Cases**: Negative coordinates, extreme values

#### 2. Activity Model Tests (`ActivityModelTest.php`)
**Primary Focus**: Model behavior and data integrity

**Key Test Areas**:
- **Attribute Casting**: JSON, date, float casting
- **Relationships**: User and export relationships
- **Pace Formatting**: Accurate pace display formatting
- **Mass Assignment**: Security and data protection
- **Factory States**: Different activity type configurations

### Test Data Factories (`database/factories/`)

#### 1. ActivityFactory
- **Base Factory**: Realistic GPS routes and activity data
- **States**: Running, cycling, hiking specific configurations
- **Complex Routes**: Figure-8 patterns for advanced testing
- **Minimal Data**: Required fields only for edge case testing

#### 2. ExportedActivityFactory
- **Format Support**: GPX, TCX, CSV sample data generation
- **Realistic Content**: Valid XML/CSV structures
- **Relationships**: Proper user and activity associations

#### 3. UserFactory (Enhanced)
- **Token States**: With tokens, without tokens, many tokens
- **Authentication**: Proper password hashing and verification

## Test Configuration

### Pest Configuration (`tests/Pest.php`)
- **Custom Expectations**: GPX validation, pace formatting, coordinate ranges
- **Helper Functions**: Sample route generation, test data creation
- **Test Case Binding**: Proper Laravel test case inheritance

### Custom Expectations
```php
expect($gpxContent)->toBeValidGpx()
expect($pace)->toHaveValidPaceFormat()
expect($value)->toBeBetween($min, $max)
expect($gpxContent)->toContainGpxTrackPoints()
```

## Running Tests

### Run All Tests
```bash
./vendor/bin/pest
```

### Run Specific Test Suites
```bash
# Feature tests only
./vendor/bin/pest tests/Feature/

# Unit tests only
./vendor/bin/pest tests/Unit/

# Specific controller tests
./vendor/bin/pest tests/Feature/ActivityExportControllerTest.php

# Specific test
./vendor/bin/pest --filter="generates valid GPX file"
```

### Run with Coverage
```bash
./vendor/bin/pest --coverage
```

## Test Coverage Areas

### 1. GPX File Generation Accuracy ✅
- XML structure validation
- Coordinate precision testing
- Timestamp accuracy
- Metadata inclusion
- Extension data (heart rate, cadence, elevation)

### 2. Route Data Integrity ✅
- Waypoint preservation
- Timestamp distribution
- Activity-specific metadata
- Distance and pace calculations

### 3. Controller Response Validation ✅
- HTTP status codes
- JSON structure validation
- Authentication requirements
- Authorization checks

### 4. Activity Type Handling ✅
- Running profile testing
- Cycling profile testing
- Hiking profile testing
- Type-specific data validation

### 5. Error Handling ✅
- Input validation
- Authentication failures
- Authorization failures
- Edge case handling
- Data consistency on failures

## Regression Testing Framework

The test suite serves as a comprehensive regression testing framework by:

1. **Automated Validation**: Every code change triggers full test suite
2. **Data Integrity Checks**: Ensures GPX files remain valid and accurate
3. **API Contract Testing**: Validates all endpoint responses
4. **Edge Case Coverage**: Tests boundary conditions and error scenarios
5. **Integration Testing**: Validates complete user workflows

## Continuous Integration

The test suite is designed to integrate with CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Tests
  run: |
    php artisan migrate --env=testing
    ./vendor/bin/pest --coverage --min=80
```

## Maintenance

### Adding New Tests
1. Create test files following naming conventions
2. Use appropriate factories for test data
3. Follow existing patterns for consistency
4. Add custom expectations for domain-specific validations

### Updating Tests
1. Update tests when API contracts change
2. Maintain backward compatibility where possible
3. Update factories when model changes occur
4. Keep test documentation current

This comprehensive test suite ensures that the FakeMyRun application maintains high quality and reliability as it evolves, with particular focus on the critical GPX generation functionality that is core to the application's purpose.
