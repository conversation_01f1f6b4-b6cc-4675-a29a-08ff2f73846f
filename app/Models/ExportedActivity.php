<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExportedActivity extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'activity_id',
        'format',
        'file_name',
        'file_path',
        'export_data'
    ];
    
    /**
     * Get the user that owns the exported activity.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * Get the activity that was exported.
     */
    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }
}
