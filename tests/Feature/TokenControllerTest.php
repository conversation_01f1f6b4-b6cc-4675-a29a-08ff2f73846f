<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create(['tokens' => 5]);
});

test('returns user token information', function () {
    Sanctum::actingAs($this->user);

    $response = $this->getJson('/api/tokens');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'tokens',
            'has_available_tokens'
        ])
        ->assertJson([
            'tokens' => 5,
            'has_available_tokens' => true
        ]);
});

test('returns false for has_available_tokens when user has no tokens', function () {
    $userWithoutTokens = User::factory()->withoutTokens()->create();
    Sanctum::actingAs($userWithoutTokens);

    $response = $this->getJson('/api/tokens');

    $response->assertStatus(200)
        ->assertJson([
            'tokens' => 0,
            'has_available_tokens' => false
        ]);
});

test('get tokens requires authentication', function () {
    $response = $this->getJson('/api/tokens');

    $response->assertStatus(401);
});

test('adds tokens to user account', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/tokens', ['tokens' => 3]);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'message',
            'tokens'
        ])
        ->assertJson([
            'message' => '3 tokens added successfully',
            'tokens' => 8 // 5 + 3
        ]);

    $this->user->refresh();
    expect($this->user->tokens)->toBe(8);
});

test('validates tokens field is required', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/tokens', []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['tokens']);
});

test('validates tokens field is integer', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/tokens', ['tokens' => 'invalid']);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['tokens']);
});

test('validates tokens field is minimum 1', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/tokens', ['tokens' => 0]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['tokens']);

    $response = $this->postJson('/api/tokens', ['tokens' => -1]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['tokens']);
});

test('adds large number of tokens', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/tokens', ['tokens' => 100]);

    $response->assertStatus(200)
        ->assertJson([
            'message' => '100 tokens added successfully',
            'tokens' => 105 // 5 + 100
        ]);

    $this->user->refresh();
    expect($this->user->tokens)->toBe(105);
});

test('add tokens requires authentication', function () {
    $response = $this->postJson('/api/tokens', ['tokens' => 3]);

    $response->assertStatus(401);
});

test('uses one token when user has available tokens', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/use-token');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'message',
            'tokens',
            'user'
        ])
        ->assertJson([
            'message' => 'Token used successfully',
            'tokens' => 4 // 5 - 1
        ]);

    $this->user->refresh();
    expect($this->user->tokens)->toBe(4);
});

test('returns user data after using token', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/use-token');

    $response->assertStatus(200)
        ->assertJsonPath('user.id', $this->user->id)
        ->assertJsonPath('user.name', $this->user->name)
        ->assertJsonPath('user.email', $this->user->email)
        ->assertJsonPath('user.tokens', 4);
});

test('rejects request when user has no tokens', function () {
    $userWithoutTokens = User::factory()->withoutTokens()->create();
    Sanctum::actingAs($userWithoutTokens);

    $response = $this->postJson('/api/use-token');

    $response->assertStatus(403)
        ->assertJson([
            'message' => 'No tokens available',
            'tokens' => 0
        ]);

    $userWithoutTokens->refresh();
    expect($userWithoutTokens->tokens)->toBe(0); // Should remain unchanged
});

test('handles edge case when user has exactly 1 token', function () {
    $userWithOneToken = User::factory()->create(['tokens' => 1]);
    Sanctum::actingAs($userWithOneToken);

    $response = $this->postJson('/api/use-token');

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Token used successfully',
            'tokens' => 0
        ]);

    $userWithOneToken->refresh();
    expect($userWithOneToken->tokens)->toBe(0);
});

test('use token requires authentication', function () {
    $response = $this->postJson('/api/use-token');

    $response->assertStatus(401);
});

test('hasAvailableTokens returns true when user has tokens', function () {
    expect($this->user->hasAvailableTokens())->toBeTrue();
});

test('hasAvailableTokens returns false when user has no tokens', function () {
    $userWithoutTokens = User::factory()->withoutTokens()->create();
    expect($userWithoutTokens->hasAvailableTokens())->toBeFalse();
});

test('useToken decrements tokens and returns true when successful', function () {
    $initialTokens = $this->user->tokens;

    $result = $this->user->useToken();

    expect($result)->toBeTrue();
    expect($this->user->tokens)->toBe($initialTokens - 1);
});

test('useToken returns false when user has no tokens', function () {
    $userWithoutTokens = User::factory()->withoutTokens()->create();

    $result = $userWithoutTokens->useToken();

    expect($result)->toBeFalse();
    expect($userWithoutTokens->tokens)->toBe(0);
});

test('useToken persists changes to database', function () {
    $initialTokens = $this->user->tokens;

    $this->user->useToken();

    $this->user->refresh();
    expect($this->user->tokens)->toBe($initialTokens - 1);
});

test('handles multiple token operations in sequence', function () {
    Sanctum::actingAs($this->user);

    // Add tokens
    $this->postJson('/api/tokens', ['tokens' => 5])
        ->assertStatus(200)
        ->assertJson(['tokens' => 10]);

    // Use a token
    $this->postJson('/api/use-token')
        ->assertStatus(200)
        ->assertJson(['tokens' => 9]);

    // Check current tokens
    $this->getJson('/api/tokens')
        ->assertStatus(200)
        ->assertJson(['tokens' => 9]);
});

test('handles concurrent token usage correctly', function () {
    Sanctum::actingAs($this->user);

    // Simulate multiple token uses
    for ($i = 0; $i < 3; $i++) {
        $response = $this->postJson('/api/use-token');
        $response->assertStatus(200);
    }

    $this->user->refresh();
    expect($this->user->tokens)->toBe(2); // 5 - 3 = 2
});

test('prevents token usage below zero', function () {
    $userWithOneToken = User::factory()->create(['tokens' => 1]);
    Sanctum::actingAs($userWithOneToken);

    // Use the last token
    $this->postJson('/api/use-token')
        ->assertStatus(200)
        ->assertJson(['tokens' => 0]);

    // Try to use another token
    $this->postJson('/api/use-token')
        ->assertStatus(403)
        ->assertJson(['message' => 'No tokens available']);

    $userWithOneToken->refresh();
    expect($userWithOneToken->tokens)->toBe(0);
});
